name: Build & Push Docker image

###############################################################################
# 1) Run automatically only on pushes to eks‑migration,
#    but keep workflow_call so other workflows can re‑use this job.
###############################################################################
on:
  push:
    branches: [ main ]

  workflow_call:
    inputs:
      node_version:
        type: number
        required: false
    secrets:
      AWS_ACCESS_KEY_ID:
        required: false
      AWS_SECRET_ACCESS_KEY:
        required: false
      AWS_DEPLOYER_ROLE:
        required: false

###############################################################################
# 2) Global variables (defined in repo/Org → Settings → Variables)
###############################################################################
env:
  AWS_REGION:               ${{ vars.AWS_REGION }}
  REGISTRY_URL:             ${{ vars.REGISTRY_URL }}
  REPOSITORY_NAME:          ${{ vars.REPOSITORY_NAME }}

###############################################################################
# 3) Build job
###############################################################################
jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      # ───── Checkout source ────────────────────────────────────────────────
      - name: Checkout repository
        uses: actions/checkout@v4

      # ───── Optional Node toolchain (driven by caller workflow) ────────────
      - name: Set up Node
        if: ${{ inputs.node_version != '' }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ inputs.node_version }}

      # ───── Image tag = first 8 chars of commit SHA ───────────────────────
      - name: Compose image tag
        run: echo "IMAGE_TAG=${GITHUB_SHA::8}" >> "$GITHUB_ENV"

      # ───── Enable Buildx --------------------------------------------------
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # ───── AWS credentials (only on “push” runs) --------------------------
      - name: Configure AWS credentials
        if: ${{ github.event_name == 'push' }}
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id:     ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region:            ${{ env.AWS_REGION }}
          role-to-assume:        ${{ secrets.AWS_DEPLOYER_ROLE }}

      # ───── Login to ECR ---------------------------------------------------
      - name: Login to Amazon ECR
        if: ${{ github.event_name == 'push' }}
        uses: aws-actions/amazon-ecr-login@v2

      # ───── Build & push ---------------------------------------------------
      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: ${{ github.event_name == 'push' }}
          tags: |
            ${{ env.REGISTRY_URL }}/${{ env.REPOSITORY_NAME }}:${{ env.IMAGE_TAG }}
