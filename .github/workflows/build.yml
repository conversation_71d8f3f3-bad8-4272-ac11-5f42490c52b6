name: Build & Push Docker Image

on:
  workflow_dispatch:
    inputs:
      client:
        type: choice
        required: false
        description: "Select a client for custom build (optional)"
        options:
          - ""
          - ariba
      environment:
        type: choice
        required: true
        options:
          - stage
          - prod
          - dev
        description: "Select environment to build for"

env:
  AWS_REGION:      ${{ vars.AWS_REGION }}
  REGISTRY_URL:    ${{ vars.REGISTRY_URL }}
  REPOSITORY_NAME: ${{ vars.REPOSITORY_NAME }}

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Compose image tag
        run: |
          ENV="${{ github.event.inputs.environment }}"
          CLIENT="${{ github.event.inputs.client }}"
          if [[ -n "$CLIENT" ]]; then
            echo "IMAGE_TAG=${GITHUB_SHA::8}-${CLIENT}-${ENV}" >> "$GITHUB_ENV"
          else
            echo "IMAGE_TAG=${GITHUB_SHA::8}-${ENV}" >> "$GITHUB_ENV"
          fi

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id:     ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region:            ${{ env.AWS_REGION }}
          role-to-assume:        ${{ secrets.AWS_DEPLOYER_ROLE }}

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v2

      - name: Set environment-specific build args
        id: args
        run: |
          ENV="${{ github.event.inputs.environment }}"
          CLIENT="${{ github.event.inputs.client }}"

          # Set default values based on environment
          if [[ "$ENV" == "stage" ]]; then
            echo "VITE_API_URL=https://qa-backend-stage.atomadvantage.ai" >> $GITHUB_ENV
            echo "VITE_WS_URL=wss://qa-backend-stage.atomadvantage.ai/api/v1/ws/user/keep_alive/" >> $GITHUB_ENV
            echo "VITE_API_KEY=284fd553d1daf5e4a2c68fabeac5ab1890c265231144a48397c520049a4f3921" >> $GITHUB_ENV
            echo "VITE_DEVELOPMENT_MODE=true" >> $GITHUB_ENV
          elif [[ "$ENV" == "prod" ]]; then
            echo "VITE_API_URL=https://api.atomadvantage.ai" >> $GITHUB_ENV
            echo "VITE_WS_URL=wss://api.atomadvantage.ai/api/v1/ws/user/keep_alive/" >> $GITHUB_ENV
            echo "VITE_API_KEY=2c08c48688c5e80a38c9020a08bd298a210a704249f0b4c422f153b79527db13" >> $GITHUB_ENV
            echo "VITE_DEVELOPMENT_MODE=false" >> $GITHUB_ENV
          elif [[ "$ENV" == "dev" ]]; then
            echo "VITE_API_URL=https://qa-backend-dev.atomadvantage.ai" >> $GITHUB_ENV
            echo "VITE_WS_URL=wss://qa-backend-dev.atomadvantage.ai/api/v1/ws/user/keep_alive/" >> $GITHUB_ENV
            echo "VITE_API_KEY=beac5ab1890c265231144a48397c520049a4f3921284fd553d1daf5e4a2c68fa" >> $GITHUB_ENV
            echo "VITE_DEVELOPMENT_MODE=true" >> $GITHUB_ENV
          else
            echo "Invalid environment"
            exit 1
          fi

          # Override for specific clients.
          # Add other client configurations below.
          if [[ "$CLIENT" == "ariba" ]]; then
            if [[ "$ENV" == "stage" ]]; then
              # TODO: Replace with actual ariba stage URLs
              echo "VITE_API_URL=https://ariba-qa-backend-stage.atomadvantage.ai" >> $GITHUB_ENV
              echo "VITE_WS_URL=wss://ariba-qa-backend-stage.atomadvantage.ai/api/v1/ws/user/keep_alive/" >> $GITHUB_ENV
            elif [[ "$ENV" == "prod" ]]; then
              # TODO: Replace with actual ariba prod URLs
              echo "VITE_API_URL=https://ariba-api.atomadvantage.ai" >> $GITHUB_ENV
              echo "VITE_WS_URL=wss://ariba-api.atomadvantage.ai/api/v1/ws/user/keep_alive/" >> $GITHUB_ENV
            elif [[ "$ENV" == "dev" ]]; then
              # TODO: Replace with actual ariba dev URLs
              echo "VITE_API_URL=https://ariba-qa-backend-dev.atomadvantage.ai" >> $GITHUB_ENV
              echo "VITE_WS_URL=wss://ariba-qa-backend-dev.atomadvantage.ai/api/v1/ws/user/keep_alive/" >> $GITHUB_ENV
            fi
          fi

          echo "VITE_VERSION=1.0.1" >> $GITHUB_ENV

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          build-args: |
            VITE_API_URL=${{ env.VITE_API_URL }}
            VITE_WS_URL=${{ env.VITE_WS_URL }}
            VITE_API_KEY=${{ env.VITE_API_KEY }}
            VITE_DEVELOPMENT_MODE=${{ env.VITE_DEVELOPMENT_MODE }}
            VITE_VERSION=${{ env.VITE_VERSION }}
          tags: |
            ${{ env.REGISTRY_URL }}/${{ env.REPOSITORY_NAME }}:${{ env.IMAGE_TAG }}
