from datetime import datetime, timedelta, timezone
from typing import cast, Literal, LiteralString, Self, Sequence
from uuid import UUI<PERSON>

from fastapi import H<PERSON>P<PERSON>x<PERSON>, status
from pydantic import BaseModel, computed_field, ConfigDict, Field, model_validator
from pydantic_core import PydanticCustomError

from ..validators import (
    validate_email_address, validate_given_and_family_names, validate_password,
    validate_user_type, validate_username
)


class UserSignupSchema(BaseModel):
    username: str

    model_config = ConfigDict(from_attributes=True)


class UserDBSchema(BaseModel):
    user_id: UUID
    username: str
    email: str
    given_name: str
    logged_in: bool
    family_name: str
    user_type: str
    login_attempt: int
    active: bool
    otp_requested: datetime | None = None
    authenticator_connect_requested: bool

    model_config = ConfigDict(from_attributes=True)


class UserSigninSchema(UserDBSchema):
    password: str


class UserListBaseFilterParams(BaseModel):
    name_query: str | None = None
    user_type: str | None = None
    status: Literal["active", "inactive"] | None = None
    allowed_clients: list[str] | None = None

    sort_by: Literal[
        "email", "given_name", "family_name", "username", "user_type", "last_online"
    ] = "email"
    sort_order: Literal["asc", "desc"] = "asc"


class UserListGetFilterParams(UserListBaseFilterParams):
    page: int = 0
    per_page: int = 10


class UserListOneUser(BaseModel):
    user_id: UUID
    username: str
    email: str
    given_name: str
    family_name: str
    password: str
    user_type: str
    login_attempt: int
    last_online: datetime | None
    active: bool
    logged_in: bool
    otp_requested: datetime | None = None
    authenticator_connect_requested: bool

    model_config = ConfigDict(from_attributes=True)

    @computed_field
    @property
    def status(self) -> str:
        if not self.last_online:
            return "inactive"
        current_time_utc: datetime = datetime.now(timezone.utc)
        time_difference: timedelta = current_time_utc - self.last_online
        minutes_difference: float = time_difference.total_seconds() // 60
        return "active" if minutes_difference <= 2 else "inactive"

    @computed_field
    @property
    def allowed_clients(self) -> list[UUID]:
        result: list[UUID] = []
        return result


class UserListGetSchema(BaseModel):
    users: Sequence[UserListOneUser]


class AddUserSchema(BaseModel):
    email: str = Field(example="<EMAIL>")
    username: str = Field(min_length=4, max_length=50, example="some_username")
    user_type: str | None = "user"
    given_name: str = Field(min_length=4, max_length=40, example="Jhon")
    family_name: str = Field(min_length=4, max_length=40, example="Smith")
    allowed_clients: list[str] = Field(example=["client_name_1", "client_name_2"])

    @model_validator(mode="before")
    def check_user_type(cls, values) -> dict:
        if "user_type" not in values:
            raise PydanticCustomError("user_type_value_error", "User role is required")

        if not values["user_type"]:
            raise PydanticCustomError(
                "user_type_value_error", "User role cannot be empty"
            )

        try:
            values["user_type"] = validate_user_type(values["user_type"])
        except HTTPException as e:
            e.detail = cast(LiteralString, e.detail)
            raise PydanticCustomError("user_type_value_error", e.detail)

        return values

    @model_validator(mode="before")
    def check_given_and_family_names(cls, values) -> dict:
        validate_given_and_family_names(
            {
                "given_name": values.get("given_name"),
                "family_name": values.get("family_name"),
            }
        )
        return values

    @model_validator(mode="before")
    def check_email(cls, values) -> dict:
        if "email" not in values:
            raise PydanticCustomError("email_value_error", "Email is required")

        if not values["email"]:
            raise PydanticCustomError("email_value_error", "Email cannot be empty")

        try:
            validate_email_address(values["email"])
        except HTTPException as e:
            e.detail = cast(LiteralString, e.detail)
            raise PydanticCustomError("email_value_error", e.detail)

        return values

    @model_validator(mode="before")
    def check_username(cls, values) -> dict:
        if "username" not in values:
            raise PydanticCustomError("username_value_error", "Username is required")

        if not values["username"]:
            raise PydanticCustomError(
                "username_value_error", "Username cannot be empty"
            )

        if "username" not in values or not values["username"]:
            raise PydanticCustomError("username_value_error", "Username is required")

        validate_username(values["username"])
        return values

    @model_validator(mode="before")
    def check_allowed_clients(cls, values) -> dict:
        if "allowed_clients" not in values:
            raise PydanticCustomError(
                "allowed_clients_value_error", "Allowed clients is required"
            )

        if not values["allowed_clients"]:
            raise PydanticCustomError(
                "allowed_clients_value_error", "Allowed clients cannot be empty"
            )

        return values


class AddUserListSchema(BaseModel):
    users: list[AddUserSchema]


class UnconfirmedUserSchema(BaseModel):
    username: str
    family_name: str
    given_name: str


class GetUnconfirmedUserFormSchema(BaseModel):
    invite_token: str


class UpdateUserSchema(BaseModel):
    user_type: str | None = None
    email: str | None = None
    username: str | None = Field(None, min_length=4, max_length=50)
    given_name: str | None = Field(None, min_length=4, max_length=40)
    family_name: str | None = Field(None, min_length=4, max_length=40)
    allowed_clients: list[str] | None = None

    @model_validator(mode="after")
    def check_user_type(self) -> Self:
        if self.user_type is not None:
            self.user_type = validate_user_type(self.user_type)
        return self

    @model_validator(mode="after")
    def check_email(self) -> Self:
        if self.email is not None:
            self.email = validate_email_address(self.email)
        return self

    @model_validator(mode="after")
    def check_given_and_family_names(self) -> Self:
        validate_given_and_family_names(
            {"given_name": self.given_name, "family_name": self.family_name}
        )
        return self


class UserLoginSchema(BaseModel):
    username: str
    password: str

    model_config = ConfigDict(from_attributes=True)

    @model_validator(mode="after")
    def check_fields(self) -> "UserLoginSchema":
        try:
            validate_username(self.username)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or username",
                headers={"WWW-Authenticate": "Bearer"},
            )
        return self


class UserSigninTokenSchema(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str
    user_type: str
    user_id: UUID | None = None
    authenticator_connect_requested: bool


class UserSignupFormSchema(BaseModel):
    username: str
    password: str = Field(
        description=(
            "Passwords must be 12-16 characters long, "
            "include at least one uppercase letter, "
            "one number, one special character, and lowercase letters."
        )
    )
    given_name: str
    family_name: str
    access_token: str

    @model_validator(mode="after")
    def check_password(self) -> Self:
        validate_password(self.password)
        return self


class TotpUriSchema(BaseModel):
    uri_type: Literal["qr_code", "text"] = "text"


class BulkUserInviteSchema(BaseModel):
    success: bool


class UserDeleteSuccessSchema(BaseModel):
    user_id: UUID
    success: bool


class UserLogoutSuccessSchema(BaseModel):
    user_id: UUID
    success: bool
