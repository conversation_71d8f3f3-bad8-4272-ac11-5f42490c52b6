import asyncio
from datetime import datetime, timedelta, UTC
from typing import Protocol, TYPE_CHECKING
from uuid import UUID

from aiohttp import BasicAuth, ClientSession
from fastapi import HTTP<PERSON>xception, WebSocket, WebSocketDisconnect
from sqlalchemy.ext.asyncio import AsyncSession

from application.schemas import UserSigninSchema
from domain.repositories import (
    MailingRepository, PacketChunkRepository, UserPacketChunkRepository, UserRepository
)
from infrastructure.database.models import Mailing
from infrastructure.database.repositories.sqlalchemy import (
    SQLAlchemyChunkRepository, SQLAlchemyMailingRepository,
    SQLAlchemyUserChunkRepository, SQLAlchemyUserRepository
)
from infrastructure.logs import get_logger
from infrastructure.services.notifications import (
    EmailNotificationService, NotificationService
)
from infrastructure.services.security.auth_token import (
    AuthTokenService, FernetAuthTokenService
)
from infrastructure.settings import Config

from . import ChunkService, ChunkServiceImpl


logger = get_logger(__name__)


if TYPE_CHECKING:
    from presentation.ws.manager import ConnectionManager


class WebSocketService(Protocol):
    async def close_user_websockets(self, user_id: UUID) -> None: ...

    async def keep_alive(self, websocket_user_session: dict) -> None: ...

    async def get_token(self, username: str) -> str: ...

    async def handle_websocket_error(
        self,
        user_id: UUID,
        websocket_id: UUID,
        reason: str,
        error_name: str,
        websocket: WebSocket,
        code: int,
    ) -> None: ...


class WebSocketServiceImlp(WebSocketService):
    def __init__(self, session: AsyncSession, connection_manager: "ConnectionManager"):
        self.session: AsyncSession = session
        self.connection_manager = connection_manager
        self.user_repository: UserRepository = SQLAlchemyUserRepository()
        self.mailing_repository: MailingRepository = SQLAlchemyMailingRepository()
        self.user_chunk_repository: UserPacketChunkRepository = (
            SQLAlchemyUserChunkRepository()
        )
        self.chunk_repository: PacketChunkRepository = SQLAlchemyChunkRepository()
        self.chunk_service: ChunkService = ChunkServiceImpl(session)
        self.email_notification_service: NotificationService = (
            EmailNotificationService()
        )
        self.auth_token_service: AuthTokenService = FernetAuthTokenService()

    async def close_user_websockets(self, user_id: UUID) -> None:
        """
        Closes the WebSocket connections for a specific user.

        This function iterates through all active WebSocket connections, checks if any
        connection belongs to the specified user, and then disconnects those WebSockets.
        It is typically used to log out or terminate the WebSocket sessions when the user
        is deleted or needs to be disconnected.

        Args:
            user_id (UUID): The ID of the user whose WebSocket connections need to be closed.

        Returns:
            None

        Raises:
            None
        """
        logger.debug(f"Close websocket for user {user_id} started execution")
        for connection in self.connection_manager.active_connections:
            connection_data: dict = list(connection.values())[0]
            user_websocket_id: UUID = list(connection.keys())[0]

            if connection_data.get("user") == user_id:
                user_websocket: WebSocket = connection_data["ws"]
                await self.connection_manager.disconnect(
                    user_websocket,
                    user_websocket_id,
                    user_id,
                    f"{user_id} account delete",
                )

        logger.debug(f"Close websocket for user {user_id} executed successfully")

    async def handle_websocket_error(
        self,
        user_id: UUID,
        websocket_id: UUID,
        reason: str,
        error_name: str,
        websocket: WebSocket,
        code: int = 1000,
    ) -> None:
        """
        Handles errors occurring in WebSocket connections for a specific user.

        This function logs the error details, disconnects the WebSocket connection,
        and checks if the user has an active WebSocket connection that needs to be updated
        or cleared from the database. It is typically used for handling WebSocket
        errors such as unexpected disconnections or failures.

        Args:
            user_id (UUID): The ID of the user whose WebSocket error is being handled.
            websocket_id (UUID): The ID of the WebSocket connection to be handled.
            reason (str): The reason for the WebSocket error or disconnection.
            error_name (str): The name of the error that occurred.
            websocket (WebSocket): The WebSocket connection object that encountered the error.
            code (int, optional): The WebSocket close code. Default is 1000, which indicates
                                a normal closure.

        Returns:
            None

        Raises:
            None
        """
        logger.debug("Handle websocket error started execution")
        logger.info(
            f"Error occured for user {user_id} and websocket "
            f"{websocket_id}: error - {error_name}; code "
            f"- {code}; reason - {reason}"
        )
        await self.connection_manager.disconnect(
            websocket, websocket_id, user_id, reason, code
        )
        # if there is not already a new connection for this user
        if websocket_id == (
            await self.user_repository.get_websocket_id_for_user(user_id, self.session)
        ):
            await self.user_repository.update_fields(
                user_id, self.session, **{"websocket_id": None}
            )
            await self.chunk_service.change_chunk_status(
                user_id, "websocket disconnect"
            )

        logger.debug("Handle websocket error executed successfully")

    async def send_email_notifications_on_unexpected_error(
        self, user_id: UUID, websocket_id: UUID, unexpected_exception: Exception
    ) -> None:
        """
        Sends an email notification to the team when an unexpected WebSocket error occurs.

        This function is designed to notify the team about any unexpected exceptions
        that occur during WebSocket communication by sending an email with details
        about the error. The email includes the exception's class and message, the
        timestamp of the error, and the associated user and WebSocket IDs.

        Args:
            user_id (UUID): The ID of the user whose WebSocket connection encountered
                            the error.
            websocket_id (UUID): The ID of the WebSocket connection where the error occurred.
            unexpected_exception (Exception): The unexpected exception that occurred.

        Returns:
            None

        Raises:
            None
        """
        logger.debug(
            "Send email notification on unexcepted websocket error started execution"
        )
        team_mailing: Mailing | None = await self.mailing_repository.get_by_name(
            "team", self.session
        )

        if not team_mailing:
            return

        team_emails: list[str] = team_mailing.mailing_emails
        exception_string: str = (
            str(unexpected_exception)
            if str(unexpected_exception)
            else "no string representation"
        )
        exception_class: str = type(unexpected_exception).__name__

        email_data: dict = {
            "exception_string": exception_string,
            "exception_class": exception_class,
            "exception_datetime": datetime.now(UTC),
            "user_id": user_id,
            "websocket_id": websocket_id,
            "enviroment": Config.RUN_ENV,
        }

        async with ClientSession(
            auth=BasicAuth("api", Config.MAILGUN_API_KEY)
        ) as aio_session:
            await self.email_notification_service.send(
                email_data,
                "websocket_unexpected_error.html",
                aio_session,
                team_emails,
                "Websocket unexpected error",
            )

        logger.debug(
            "Send email notification on unexcepted websocket error executed successfully"
        )

    async def keep_alive(self, websocket_user_session: dict) -> None:
        """
        Maintains an active WebSocket connection for the user and handles
        the keep-alive functionality, including checking for user activity,
        handling disconnections, and updating chunk statuses.

        This function handles maintaining the WebSocket connection for the
        user, checking for any logout or timeout signals, and handling
        unexpected errors that may occur. If an old connection is detected
        for the same user, it disconnects the previous one and updates the
        WebSocket ID. Additionally, it updates the user's last opened chunk
        if available.

        Args:
            websocket_user_session (dict): A dictionary containing session
                                        information for the WebSocket,
                                        including the WebSocket object,
                                        user details, session,
                                        and WebSocket ID.

        Returns:
            None

        Raises:
            WebSocketDisconnect: If the WebSocket connection is disconnected
                                due to various reasons (e.g., timeout, logout).
            Exception: If an unexpected error occurs during the keep-alive process.
        """
        logger.debug("Keep alive started execution")

        if not websocket_user_session:
            return
        websocket: WebSocket = websocket_user_session["websocket"]
        user: UserSigninSchema = websocket_user_session["user"]
        session: AsyncSession = websocket_user_session["session"]
        websocket_id: UUID = websocket_user_session["websocket_id"]
        user_id: UUID = user.user_id
        try:
            # todo remove checking in memory
            # todo needs discussion about whether it would be okay to keep two tabs(websockets) open at the same time
            # todo for a time equals between websocket updates
            # todo if it is okay, than delete check if any(
            #                 user_id == connection[(list(connection.keys())[0])]['user']
            #                 for connection in connection_manager.active_connections
            #         ):

            if any(
                user_id == connection[(list(connection.keys())[0])]["user"]
                for connection in self.connection_manager.active_connections
            ):
                # remove old websocket for this user
                indices: list[int] = [
                    index
                    for index, connection in enumerate(
                        self.connection_manager.active_connections
                    )
                    if user_id == connection[(list(connection.keys())[0])]["user"]
                ]
                connection: dict[UUID, dict] = (
                    self.connection_manager.active_connections[indices[0]]
                )
                con_websocket_id: UUID = list(connection.keys())[0]
                reason: str = "Same user different connection"
                logger.info(
                    f"Removing old connection for user {user_id} from websocket "
                    f"{con_websocket_id} with reason: {reason}"
                )
                await self.connection_manager.connect(websocket, websocket_id, user_id)
                await self.user_repository.update_fields(
                    user_id, session, **{"websocket_id": websocket_id}
                )
                await self.connection_manager.disconnect(  # todo handle WebsocketDisconnect for old websocket
                    connection[con_websocket_id]["ws"],
                    con_websocket_id,
                    user_id,
                    reason,
                )
            else:
                await self.connection_manager.connect(websocket, websocket_id, user_id)
                await self.user_repository.update_fields(
                    user_id, session, **{"websocket_id": websocket_id}
                )

            while True:
                try:
                    data: dict = await asyncio.wait_for(
                        websocket.receive_json(), timeout=Config.keep_alive_timeout
                    )

                    if data.get("alive"):
                        await self.user_repository.update_fields(
                            user_id, session, **{"last_online": datetime.now(UTC)}
                        )
                        await session.commit()
                        logger.debug(f"Updated last_online for user {user_id} via WebSocket keepalive")

                    if data.get("logout"):
                        logger.info(
                            f"Keep alive: logout message for user {user_id} "
                            f"and websocket {websocket_id}"
                        )
                        raise WebSocketDisconnect(reason="Logout")

                except WebSocketDisconnect as ws_error:
                    if ws_error.reason != "Logout":
                        logger.info(
                            f"Keep alive: ws_error in 60s for user {user_id} "
                            f"and websocket {websocket_id}. Error: {str(ws_error)}, "
                            f"reason: {ws_error.reason or 'Not provided'}"
                        )
                        await asyncio.sleep(
                            60
                        )  # give user 1 minute to establish a new connection before removing old
                    raise ws_error

                except RuntimeError as re:
                    reason: str = (
                        f"Keep alive: RuntimeError occured, application state "
                        f"{websocket.application_state}, client state {websocket.client_state}"
                    )
                    logger.error(f"{reason}. Error details: {str(re)}")
                    raise WebSocketDisconnect(code=1006, reason=reason)

                except (asyncio.TimeoutError, HTTPException) as e:
                    logger.info(
                        f"Keep alive: {str(e)} error for user {user_id} "
                        f"and websocket {websocket_id}"
                    )
                    reason: str = (
                        "TimeoutError"
                        if isinstance(e, asyncio.TimeoutError)
                        else str(e)
                    )
                    raise WebSocketDisconnect(reason=reason)

                finally:
                    temp_chunk_id: (
                        UUID | None
                    ) = await self.user_chunk_repository.find_last_opened_packet_chunk_review_status(
                        session, user_id
                    )
                    if temp_chunk_id:
                        await self.user_chunk_repository.update_fields(
                            user_id, temp_chunk_id, session
                        )
                        await self.chunk_repository.update_fields(
                            temp_chunk_id, session
                        )
                        logger.info(
                            f"Keep alive: updated last_opened field "
                            f"for user {user_id} and chunk {temp_chunk_id}"
                        )
                    else:
                        logger.info(
                            f"Keep alive: user {user_id} has no "
                            f"chunks to update last_opened field"
                        )

        except WebSocketDisconnect as ws_error:
            await self.handle_websocket_error(
                user_id=user_id,
                websocket_id=websocket_id,
                reason=ws_error.reason or "Not provided",
                error_name="WebSocketDisconnect",
                websocket=websocket,
                code=ws_error.code,
            )

        except Exception as e:
            error_name: str = type(e).__name__
            logger.critical(
                f"Keep alive: unexcepted error in "
                f"websocket - {error_name}. Details: {str(e)}"
            )

            await self.handle_websocket_error(
                user_id=user_id,
                websocket_id=websocket_id,
                reason=str(e) or "Not provided",
                error_name=error_name,
                websocket=websocket,
                code=1011,
            )
            await self.send_email_notifications_on_unexpected_error(
                user_id, websocket_id, e
            )

        finally:
            logger.info(
                f"Keep alive: final for user {user_id} and websocket "
                f"{websocket_id}. Processing to websocket disconnect..."
            )
            await self.connection_manager.disconnect(
                websocket, websocket_id, user_id, "finale"
            )

        logger.debug("Keep alive executed successfully")

    async def get_token(self, username: str) -> str:
        """
        Generates a WebSocket authentication token for the given username.

        This function creates a short-lived WebSocket token using the
        provided username. The token is generated with a 10-second expiry
        time and can be used for authenticating the WebSocket connection.

        Args:
            username (str): The username for which the WebSocket token is generated.

        Returns:
            str: The generated WebSocket authentication token.

        Raises:
            Exception: If there is an error generating the authentication token.
        """
        logger.debug("Get websocket token started execution")
        data_to_encode: dict[str, str] = {"username": username}
        websocket_token: str = await self.auth_token_service.create_auth_token(
            data=data_to_encode, expires_delta=timedelta(seconds=10)
        )
        logger.debug("Get websocket token executed successfully")
        return websocket_token
