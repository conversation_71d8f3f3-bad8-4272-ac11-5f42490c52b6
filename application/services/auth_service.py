from datetime import datetime, timed<PERSON>ta, UTC
from typing import Protocol

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

from application.schemas import (
    UserLoginSchema, UserLogoutSuccessSchema, UserSigninSchema, UserListOneUser
)
from domain.repositories import UserActivityLogRepository, UserRepository
from infrastructure.database.repositories.sqlalchemy import (
    SQLAlchemyUserActivityLogRepository, SQLAlchemyUserRepository
)
from infrastructure.logs import get_logger
from infrastructure.services.notifications import (
    EmailNotificationService, NotificationService
)
from infrastructure.services.security import JWTService, PyJWTService, TwoFactorAuth
from infrastructure.services.security.auth_token import (
    AuthTokenService, FernetAuthTokenService
)
from infrastructure.services.security.hash import HashService, PasswordHashService
from infrastructure.settings import Config
from utils import convert_uuids_to_strings

from .user_service import UserService, UserServiceImpl


logger = get_logger(__name__)


class AuthService(Protocol):
    async def authenticate_user(
        self, username: str, password: str, *args, **kwargs
    ) -> tuple[UserListOneUser | None, bool]: ...

    async def login(self, *args, **kwargs) -> dict: ...

    async def signup(self, *args, **kwargs) -> str: ...

    async def otp_verify(self, *args, **kwargs) -> None: ...

    async def otp_enable(self, *args, **kwargs) -> bytes | str: ...

    async def logout(self, user: UserListOneUser) -> dict: ...

    async def get_user_from_token(self, token: str) -> UserListOneUser: ...


class AuthServiceImpl(AuthService):
    def __init__(self, session: AsyncSession):
        self.session: AsyncSession = session
        self.user_service: UserService = UserServiceImpl(session)
        self.email_notification_service: NotificationService = (
            EmailNotificationService()
        )
        self.jwt_service: JWTService = PyJWTService()
        self.password_hash_service: HashService = PasswordHashService()
        self.auth_token_service: AuthTokenService = FernetAuthTokenService()
        self.user_activity_logs_repository: UserActivityLogRepository = (
            SQLAlchemyUserActivityLogRepository()
        )
        self.user_repository: UserRepository = SQLAlchemyUserRepository()

    async def authenticate_user(
        self, username: str, password: str
    ) -> tuple[UserListOneUser | None, bool]:
        """
        Authenticates a user based on provided username and password.

        This function retrieves the user by `username`, verifies if the user is active, checks if they are logged in already,
        and validates the password. If any validation fails, an appropriate HTTP exception is raised. If the login is successful,
        it logs the activity and returns the user and a success flag.

        Args:
            username (str): The username of the user attempting to authenticate.
            password (str): The password provided by the user for authentication.

        Returns:
            tuple[UserListOneUser | None, bool]: A tuple where the first element is the user object (or None if not found),
                                                and the second element is a boolean indicating whether authentication succeeded.

        Raises:
            HTTPException:
                - If the user is already logged in, an unauthorized error is raised with a relevant message.
                - If the user account is disabled, an unauthorized error is raised indicating the account is blocked.
                - If the password is incorrect, an HTTP 401 error is raised indicating the failure.
        """
        logger.debug(f"Authenticate user started execution for username: {username}")

        user: UserListOneUser | None = await self.user_service.get_user(username)

        if not user:
            logger.info(f"Authenticate user failed for username '{username}'")
            return user, False

        if user.logged_in:
            logger.info(
                f"Authenticate user failed for username "
                f"'{username}', user already logged in"
            )
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Cannot log in while you have an active session",
                headers={"WWW-Authenticate": "Bearer"},
            )

        if not user.active:
            await self.user_activity_logs_repository.create_user_activity_log(
                {"user_id": user.user_id, "action": "login", "description": "blocked"},
                self.session,
            )
            logger.info(
                f"Authenticate user failed for username "
                f"'{username}', user account is disabled"
            )
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User account is disabled",
                headers={"WWW-Authenticate": "Bearer"},
            )

        if not await self.password_hash_service.verify(password, user.password):
            await self.user_activity_logs_repository.create_user_activity_log(
                {"user_id": user.user_id, "action": "login", "description": "failed"},
                self.session,
            )
            logger.info(
                f"Authenticate user failed for username "
                f"'{username}', user entered wrong password"
            )
            return user, False

        await self.user_activity_logs_repository.create_user_activity_log(
            {"user_id": user.user_id, "action": "login", "description": "successful"},
            self.session,
        )
        logger.debug(
            f"Authenticate user executed successfully for username: {username}"
        )

        return user, True

    async def login(self, request: Request, form_data) -> dict:
        """
        Authenticates a user and returns access and refresh tokens.

        This function handles user login via either password authentication or a refresh token.
        If `grant_type` is `refresh_token`, it validates the token and retrieves the user.
        Otherwise, it verifies the username and password. Upon successful authentication,
        it generates and returns new access and refresh tokens.

        Args:
            request (Request): The HTTP request object containing client details.
            form_data: The login form data, which includes the `grant_type`, and depending on the type,
                    may contain a username, password, or refresh token.

        Returns:
            dict: A dictionary containing:
                - `access_token` (str): The generated access token.
                - `refresh_token` (str): The generated refresh token.
                - `token_type` (str): The token type, which is always "Bearer".
                - `user_type` (str): The type of the authenticated user.
                - `user_id` (str): The unique identifier of the user.
                - `authenticator_connect_requested` (bool): Whether the user has requested multi-factor authentication setup.

        Raises:
            HTTPException:
                - If authentication fails due to an incorrect username or password,
                an HTTP 401 error is raised with the message "Incorrect email or password".
                - If an invalid refresh token is provided, authentication will also fail.
        """
        if form_data.grant_type == "refresh_token":
            logger.debug("Login user by refresh token started execution")
            _, username = await self.jwt_service.validate_refresh_token(
                form_data.refresh_token
            )
            user_: UserListOneUser | None = await self.user_service.get_user(username)
            success = True
        else:
            logger.debug("Login user by password started execution")
            user_login_data: UserLoginSchema = UserLoginSchema.model_validate(form_data)
            user_, success = await self.authenticate_user(
                user_login_data.username, user_login_data.password
            )
            if user_:
                await self.user_service.update_user_fields_on_login(
                    request, user_, success
                )

        if not user_ or not success:
            logger.info(
                f"Login user {form_data.username} failed: incorrent email or password"
            )
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        data_to_encode: dict[str, str] = {
            "sub": user_.username,
            "user_type": user_.user_type,
        }

        access_token: tuple[
            str, int | None
        ] = await self.jwt_service.create_access_token(data=data_to_encode)
        refresh_token: tuple[
            str, int | None
        ] = await self.jwt_service.create_refresh_token(data=data_to_encode)
        content: dict = {
            "access_token": access_token[0],
            "refresh_token": refresh_token[0],
            "token_type": "Bearer",
            "user_type": user_.user_type,
            "user_id": str(user_.user_id),
            "authenticator_connect_requested": user_.authenticator_connect_requested,
        }

        logger.debug(
            f"Login user {form_data.username} by "
            f"{form_data.grant_type} executed successfully"
        )
        return content

    async def signup(self, form_data, request: Request) -> str:
        """
        Registers a new user and activates their account.

        This function processes user signup by decoding the provided authentication token,
        retrieving the user from the database, hashing their password, updating user details,
        and sending a welcome email upon successful registration.

        Args:
            form_data: The signup form data containing user credentials and personal details.
            request (Request): The HTTP request object, used for sending an email notification.

        Returns:
            str: The username of the successfully registered user.

        Raises:
            HTTPException:
                - If the provided authentication token is invalid or does not correspond
                to an unconfirmed user, an HTTP 401 error is raised with the message
                "Could not find user".
        """
        payload: dict = await self.auth_token_service.decode_auth_token(
            form_data.access_token
        )
        payload_username: str = payload.get("username", "")
        logger.debug(f"Signup user started execution for username {payload_username}")

        user: UserListOneUser | None = await self.user_service.get_user(
            payload_username
        )
        if user is None:
            logger.info(
                f"Signup user failed for username {payload_username}: "
                f"couldn't find unconfirmed user in token"
            )
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Could not find user"
            )

        hashed_password: str = await self.password_hash_service.get_hashed_value(
            form_data.password
        )
        await self.user_repository.update_fields(
            user.user_id,
            self.session,
            **{
                "username": form_data.username,
                "password": hashed_password,
                "given_name": form_data.given_name,
                "family_name": form_data.family_name,
                "active": True,
            },
        )

        email_data: dict = {
            "given_name": form_data.given_name,
            "family_name": form_data.family_name,
            "login_link": f"{Config.FRONTEND_URL}/login",
        }

        await self.email_notification_service.send(
            email_data,
            "welcome_mail.html",
            request.state.aio_session,
            user.email,
            "Welcome to Record Ranger",
        )

        logger.debug(
            f"Signup user executed successfully for username {payload_username}"
        )
        return payload_username

    async def otp_verify(self, access_token: str, totp_code: str) -> None:
        """
        Verifies a one-time password (OTP) for two-factor authentication.

        This function decodes the provided access token to identify the user, retrieves their details,
        and verifies whether the OTP code is valid. If the code is expired, invalid, or the user does not exist,
        an appropriate HTTP exception is raised. Upon successful verification, the user's authentication state
        is updated, and a log entry is recorded.

        Args:
            access_token (str): The JWT access token used to identify the user.
            totp_code (str): The one-time password (OTP) provided by the user.

        Returns:
            None

        Raises:
            HTTPException:
                - If the user is not found, an HTTP 401 error is raised with the message "Could not find user".
                - If the OTP code has expired (older than 5 minutes), an HTTP 401 error is raised with the message "Code expired".
                - If the OTP code is incorrect, an HTTP 401 error is raised with the message "Invalid code".
        """
        _, username = await self.jwt_service.decode_token(
            access_token, Config.JWT_SECRET_KEY
        )
        user: UserListOneUser | None = await self.user_service.get_user(username)
        logger.debug(f"OTP verify started execution for username {username}")

        if user is None:
            logger.info(
                f"OTP verify failed for username {username}: could not find user"
            )
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Could not find user"
            )

        if not user.otp_requested or user.otp_requested < datetime.now(UTC) - timedelta(
            minutes=5
        ):
            logger.info(f"OTP verify failed for username {username}: code expired")
            await self.user_activity_logs_repository.create_user_activity_log(
                {
                    "user_id": user.user_id,
                    "action": "otp_verify",
                    "description": "code expired",
                },
                self.session,
            )
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Code expired"
            )

        secret_key: str = await TwoFactorAuth.get_or_create_secret_key(
            username, self.session
        )
        two_factor_auth: TwoFactorAuth = TwoFactorAuth(user.username, secret_key)

        is_valid: bool = two_factor_auth.verify_totp_code(totp_code)
        if not is_valid:
            logger.info(f"OTP verify failed for username {username}: code invalid")
            await self.user_activity_logs_repository.create_user_activity_log(
                {
                    "user_id": user.user_id,
                    "action": "otp_verify",
                    "description": "code invalid",
                },
                self.session,
            )
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid code"
            )
        await self.user_repository.update_fields(
            user.user_id,
            self.session,
            **{
                "otp_requested": None,
                "authenticator_connect_requested": True,
                "logged_in": True,
            },
        )  # set in the /token endpoint

        await self.user_activity_logs_repository.create_user_activity_log(
            {
                "user_id": user.user_id,
                "action": "otp_verify",
                "description": "successful",
            },
            self.session,
        )
        logger.debug(f"OTP verify executed successfully for username {username}")

    async def otp_enable(self, access_token: str, uri_type: str) -> bytes | str:
        """
        Enables two-factor authentication (2FA) for a user.

        This function verifies the user's identity using an access token, checks if 2FA is already enabled,
        and generates a QR code or URI for setting up an authenticator app. If the user is not found
        or 2FA is already enabled, an appropriate HTTP exception is raised.

        Args:
            access_token (str): The JWT access token used to identify the user.
            uri_type (str): The type of URI to return. Can be "qr_code" for a QR code image
                            or any other value for a standard authentication URI.

        Returns:
            bytes | str: A QR code image in bytes if `uri_type` is "qr_code", otherwise a URI string.

        Raises:
            HTTPException:
                - If the user is not found, an HTTP 401 error is raised with the message "Could not find user".
                - If the user has already connected an authenticator, an HTTP 401 error is raised with the message
                "Authenticator already connected".
                - If the QR code generation fails, an HTTP 401 error is raised with the message "Could not find user".
        """
        logger.debug("OTP enable started execution")

        _, username = await self.jwt_service.decode_token(
            access_token, Config.JWT_SECRET_KEY
        )
        user: UserListOneUser | None = await self.user_service.get_user(username)
        if user is None:
            logger.info("OTP enable failed: could not find user in token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Could not find user"
            )

        if await self.user_repository.get_authenticator_connect_requested_by_id(
            user.user_id, self.session
        ):
            logger.info(
                f"OTP enable failed: authenticator "
                f"already connected for user {user.username}"
            )
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authenticator already connected",
            )
        secret_key: str = await TwoFactorAuth.get_or_create_secret_key(
            username, self.session
        )
        two_factor_auth: TwoFactorAuth = TwoFactorAuth(user.username, secret_key)

        logger.debug("OTP enable executed successfully")
        if uri_type == "qr_code":
            qr_code: bytes = two_factor_auth.qr_code
            if qr_code is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Could not find user",
                )
            return qr_code
        else:
            return two_factor_auth.uri_for_app

    async def get_user_from_token(self, token: str) -> UserListOneUser:
        """
        Retrieves a user from a given authentication token.

        This function decodes the token, fetches the corresponding user, and performs
        various checks, such as whether the user exists, is active, and requires OTP authentication.
        If any of these checks fail, an HTTP exception is raised.

        Args:
            token (str): The JWT access token used to identify and retrieve the user.

        Returns:
            UserListOneUser: The authenticated user object.

        Raises:
            HTTPException:
                - If the user is not found, an HTTP 401 error is raised with the message "Could not find user".
                - If the user is disabled, an HTTP 401 error is raised with the message "User account is disabled".
                - If the user requires two-factor authentication, an HTTP 401 error is raised with the message
                "Two-Factor authentication required".
        """
        logger.debug("Get user from token started execution")
        payload, username = await self.jwt_service.decode_token(
            token, Config.JWT_SECRET_KEY
        )
        user: UserListOneUser | None = await self.user_service.get_user(username)
        if user is None:
            logger.info("Get user from token failed: could not find user from token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Could not find user"
            )

        if not user.active:
            logger.info(f"Get user from token failed: user {user.username} is disabled")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User account is disabled",
                headers={"WWW-Authenticate": "Bearer"},
            )

        if user.otp_requested:
            logger.info(
                f"Get user from token failed: OTP "
                f"auth required for user {user.username}"
            )
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Two-Factor authentication required",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Update last_online field - FIX THE BUG
        await self.user_repository.update_fields(
            user.user_id, 
            self.session, 
            **{"last_online": datetime.now(UTC)}  # Actually update the field!
        )
        await self.session.commit()  # Ensure the update is committed
        
        logger.debug("Get user from token executed successfully")
        return user

    async def logout(self, user: UserListOneUser) -> dict:
        """
        Logs out a user and updates their session status.

        This function creates an activity log entry indicating the user's logout action and then updates the user's status
        to `logged_in = False` in the database. It ensures that the user is properly logged out, and returns a dictionary
        indicating the success of the operation.

        Args:
            user (UserListOneUser): The authenticated user who is logging out.

        Returns:
            dict: A dictionary containing the `user_id` and a success flag (`success`).

        Raises:
            HTTPException: If any error occurs during the logout process, such as database issues or activity log creation failures.
        """
        logger.debug(f"Logout user started execution for user: {user.username}")
        await self.user_activity_logs_repository.create_user_activity_log(
            {"user_id": user.user_id, "action": "logout", "description": "successful"},
            self.session,
        )
        await self.user_repository.update_fields(
            user.user_id, self.session, **{"logged_in": False}
        )
        result: dict = await convert_uuids_to_strings(
            UserLogoutSuccessSchema.model_validate(
                {"user_id": user.user_id, "success": True}
            ).model_dump()
        )
        logger.debug(f"Logout user executed successfully for user: {user.username}")
        return result
