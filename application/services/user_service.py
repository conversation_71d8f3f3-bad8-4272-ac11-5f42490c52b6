import asyncio
import secrets
from datetime import datetime, timedelta, timezone, UTC
from typing import Any, Coroutine, Protocol, Sequence
from uuid import UUID

import pandas as pd
from fastapi import HTTPException, Request, status, UploadFile
from fastapi.encoders import jsonable_encoder
from pydantic import ValidationError
from sqlalchemy import and_, asc, Column, ColumnElement, desc, or_, UnaryExpression
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import InstrumentedAttribute

from application.schemas import (
    AddUserListSchema, AddUserSchema, GetUnconfirmedUserFormSchema,
    UnconfirmedUserSchema, UpdateUserSchema, UserListBaseFilterParams,
    UserListGetFilterParams, UserListGetSchema, UserSigninSchema, UserListOneUser
)
from application.services.chunk_service import ChunkService, ChunkServiceImpl
from application.validators import (
    validate_email_address, validate_file_size, validate_password
)
from domain.repositories import (
    ClientRepository, UserActivityLogRepository, UserClientRepository, UserRepository
)
from infrastructure.database.connection_config import commit_session
from infrastructure.database.models import User
from infrastructure.database.repositories.sqlalchemy import (
    SQLAlchemyClientRepository, SQLAlchemyUserActivityLogRepository,
    SQLAlchemyUserClientRepository, SQLAlchemyUserRepository
)
from infrastructure.logs import get_logger
from infrastructure.services.files import CsvFileService, CsvService
from infrastructure.services.notifications import (
    EmailNotificationService, NotificationService
)
from infrastructure.services.security import (
    AuthTokenService, FernetAuthTokenService, HashService, PasswordHashService
)
from infrastructure.settings import Config
from utils import convert_uuids_to_strings


logger = get_logger(__name__)


class UserService(Protocol):
    async def get_user(self, username: str) -> UserListOneUser | None: ...

    async def update_user_fields_on_login(
        self, request: Request, user_: UserListOneUser, success: bool
    ) -> None: ...

    async def get_all(
        self, user_type: str, filter_params: UserListGetFilterParams
    ) -> tuple[list[dict], int]: ...

    async def get_all_for_csv_export(
        self, user_type: str, filter_params: UserListBaseFilterParams
    ) -> str: ...

    async def add_users(
        self, request: Request, user_type: str, users_list_schema: AddUserListSchema
    ) -> bool: ...

    async def add_users_csv(
        self, request: Request, user_type: str, csv_file: UploadFile
    ) -> bool: ...

    async def get_unconfirmed_user(
        self, form_data: GetUnconfirmedUserFormSchema
    ) -> dict: ...

    async def delete_user(
        self, request: Request, user_id: UUID, user_type: str, delete_user_id: UUID
    ) -> bool: ...

    async def reset_password(self, request: Request, email: str) -> bool: ...

    async def confirm_reset_password(
        self, confirmation_token: str, new_password: str
    ) -> bool: ...

    async def update_user(
        self,
        request: Request,
        user_id: UUID,
        user_type: str,
        update_user_info: UpdateUserSchema,
        update_user_id: UUID,
    ) -> User: ...


class UserServiceImpl(UserService):
    def __init__(self, session: AsyncSession):
        self.session: AsyncSession = session
        self.password_hash_service: HashService = PasswordHashService()
        self.chunk_service: ChunkService = ChunkServiceImpl(session)
        self.csv_service: CsvService = CsvFileService()
        self.email_notification_service: NotificationService = (
            EmailNotificationService()
        )
        self.auth_token_service: AuthTokenService = FernetAuthTokenService()
        self.user_repository: UserRepository = SQLAlchemyUserRepository()
        self.user_client_repository: UserClientRepository = (
            SQLAlchemyUserClientRepository()
        )
        self.user_activity_logs_repository: UserActivityLogRepository = (
            SQLAlchemyUserActivityLogRepository()
        )
        self.client_repository: ClientRepository = SQLAlchemyClientRepository()

    async def _add_filter_expressions(
        self, filters: list[dict], use_ilike: bool = False
    ) -> list[ColumnElement]:
        """
        Adds filter expressions to a list of SQLAlchemy queries based on provided filters.

        This function iterates over a list of filter dictionaries, where each dictionary contains a column and a value.
        It creates SQLAlchemy expressions to filter the data based on the column and value. If `use_ilike` is set to True,
        the function will generate `ILIKE` expressions for partial matching. Otherwise, it creates equality comparisons.

        Args:
            filters (list[dict]): A list of dictionaries where each dictionary contains a 'column' (SQLAlchemy Column)
                and 'value' (the value to filter by).
            use_ilike (bool, optional): If True, uses the `ILIKE` operator for case-insensitive partial matching.
                Defaults to False, which uses equality comparison.

        Returns:
            list[ColumnElement]: A list of SQLAlchemy filter expressions that can be added to a query.

        """
        logger.debug(
            f"Add filter expression started execution "
            f"with filters: {filters}, ilike - {use_ilike}"
        )

        result: list = []

        for f in filters:
            col: Column = f["column"]
            val: Any = f["value"]

            if not val:
                continue

            if use_ilike:
                expression: ColumnElement = or_(col == val, col.ilike(f"%{val}%"))
            else:
                expression: ColumnElement = col == val

            result.append(expression)

        logger.debug(
            f"Add filter expression executed successfully "
            f"with filters: {filters}, ilike - {use_ilike}"
        )
        return result

    async def _get_users_csv_dataframe(self, users: list) -> pd.DataFrame:
        """
        Converts a list of user objects into a pandas DataFrame.

        This function takes a list of user objects, extracts the key-value pairs from each user, and converts them into
        a DataFrame. Each user's attributes are represented as columns in the resulting DataFrame.

        Args:
            users (list): A list of user objects where each user is typically represented as a dictionary or an object
                with key-value pairs representing the user's fields.

        Returns:
            pd.DataFrame: A pandas DataFrame containing all users' data, with each user as a row and the user fields
                as columns.
        """
        logger.debug("Get users csv dataframe started execution")

        all_users_list: list[dict] = []
        for user_ in users:
            user_dict = {field[0]: field[1] for field in user_.items()}
            all_users_list.append(user_dict)

        df = pd.DataFrame(all_users_list)
        logger.debug("Get users csv dataframe executed successfully")
        return df

    async def _check_email_and_username_in_use(
        self,
        errors_dict: dict,
        line_idx: int,
        email: str | None = None,
        username: str | None = None,
        duplicate_email_in_data: bool | None = None,
        duplicate_username_in_data: bool | None = None,
    ) -> bool:
        """
        Checks if the provided email and username are already in use.

        This function checks whether the given email or username is already associated with an existing user in the system,
        or if there is a duplicate email or username in the provided data. It adds appropriate error descriptions to the
        `errors_dict` if duplicates are found.

        Args:
            errors_dict (dict): A dictionary that stores errors, where the function appends non-unique email and username
                errors.
            line_idx (int): The line index (starting from 0) in the data source where the email and username are located.
            email (str | None, optional): The email to check. Defaults to None.
            username (str | None, optional): The username to check. Defaults to None.
            duplicate_email_in_data (bool | None, optional): A flag indicating if the email is a duplicate within the data.
                Defaults to None.
            duplicate_username_in_data (bool | None, optional): A flag indicating if the username is a duplicate within the
                data. Defaults to None.

        Returns:
            bool: Returns True if either the email or username is found to be non-unique, otherwise False.
        """
        logger.debug("Check email and username in use started execution")
        unique_error: bool = False

        if email and (
            await self.user_repository.get_by_email(email, self.session)
            or duplicate_email_in_data
        ):
            error_description: dict = {"value": email, "line": line_idx + 1}
            errors_dict.get("errors").get("non_unique_emails").append(error_description)
            unique_error = True

        if username and (
            await self.user_repository.get_by_username(username, self.session)
            or duplicate_username_in_data
        ):
            error_description: dict = {"value": username, "line": line_idx + 1}
            errors_dict.get("errors").get("non_unique_usernames").append(
                error_description
            )
            unique_error = True

        logger.debug("Check email and username in use executed successfully")
        return unique_error

    async def get_user(self, username: str) -> UserListOneUser | None:
        """
        Retrieves a user by their email or username.

        This function attempts to retrieve a user based on the provided username. It first tries to validate the username
        as an email and fetch the user by email. If the username is not a valid email, it falls back to searching for the
        user by their username. If the user is found, the function returns a `UserListOneUser` object. Otherwise, it returns None.

        Args:
            username (str): The username or email address of the user to retrieve.

        Returns:
            UserListOneUser | None: A validated user object represented by `UserListOneUser` if found, otherwise None.
        """
        logger.debug(f"Get user started execution for username: {username}")
        user: User | None = None
        try:
            validated_email: str = validate_email_address(username)
            user = await self.user_repository.get_by_email(
                validated_email, self.session
            )

        except HTTPException:
            user = await self.user_repository.get_by_username(username, self.session)

        finally:
            logger.debug(f"Get user executed successfully for username: {username}")
            return UserListOneUser.model_validate(user) if user else None

    async def update_user_fields_on_login(
        self, request: Request, user_: UserListOneUser, success: bool
    ) -> None:
        """
        Updates user fields based on the outcome of a login attempt.

        This function updates various fields of a user during the login process. If the login is successful, the
        number of login attempts is reset. If the login is unsuccessful, the login attempt counter is incremented,
        and after three failed attempts, the user's account is deactivated. Additionally, if there are too many failed
        attempts, a password reset is triggered. User activity logs are also created for failed login attempts.

        Args:
            request (Request): The incoming HTTP request.
            user_ (UserListOneUser): The user attempting to log in.
            success (bool): A boolean indicating whether the login attempt was successful.

        Returns:
            None
        """
        logger.debug(
            f"Update user fields on login started execution for user: {user_.username}"
        )
        await self.user_repository.update_fields(
            user_.user_id, self.session, **{"otp_requested": datetime.now(UTC)}
        )  # cleared in another endpoint when entering otp code

        if success:
            await self.user_repository.update_fields(
                user_.user_id, self.session, **{"login_attempt": 0}
            )

        else:
            if user_.login_attempt == 2:  # if it is already third attempt
                await self.user_repository.update_fields(
                    user_.user_id, self.session, **{"active": False}
                )
                await self.reset_password(request, user_.email)
            await self.user_repository.update_fields(
                user_.user_id,
                self.session,
                **{"login_attempt": user_.login_attempt + 1},
            )

            await self.user_activity_logs_repository.create_user_activity_log(
                {
                    "user_id": user_.user_id,
                    "action": "login",
                    "description": f"{user_.user_id} failed login and now has {user_.login_attempt + 1} login attempt",
                },
                self.session,
            )

        logger.debug(
            f"Update user fields on login executed "
            f"successfully for user: {user_.username}"
        )

    async def get_all(
        self, user_type: str, filter_params: UserListGetFilterParams
    ) -> tuple[list[dict], int]:
        """
        Retrieves a list of users with the specified filters and pagination.

        This function fetches a list of users, with filtering and sorting options, based on the user's permissions.
        Only a user with the type "manager" can access the list of users. It also supports filtering by user attributes
        (like email, username, given name, family name, and status), pagination, and sorting. The function also includes
        information about allowed clients for each user.

        Args:
            user_type (str): The type of the user making the request. Only a "manager" can access all users.
            filter_params (UserListGetFilterParams): An object containing the filter parameters for pagination,
                                                    sorting, and other filter conditions.

        Returns:
            tuple[list[dict], int]: A tuple where the first element is a list of user data in dictionary form,
                                    and the second element is the total number of users matching the filters.

        Raises:
            HTTPException: If the user does not have the necessary permissions (i.e., not a manager).
        """
        logger.debug("Get all users started execution")

        if user_type != "manager":
            logger.info("Get all users failed: user has no enough permissions")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Not enough permissions"
            )

        start: int = filter_params.page * filter_params.per_page
        stop: int = start + filter_params.per_page

        order_by_column: Column | InstrumentedAttribute[str] = getattr(
            User, filter_params.sort_by, User.email
        )
        sort_clause: UnaryExpression = (
            desc(order_by_column)
            if filter_params.sort_order == "desc"
            else asc(order_by_column)
        )

        filter_conditions: list = []
        filters_with_contains: list[ColumnElement] = await self._add_filter_expressions(
            filters=[
                {"column": User.email, "value": filter_params.name_query},
                {"column": User.username, "value": filter_params.name_query},
                {"column": User.given_name, "value": filter_params.name_query},
                {"column": User.family_name, "value": filter_params.name_query},
            ],
            use_ilike=True,
        )

        filters_by_equality: list[ColumnElement] = await self._add_filter_expressions(
            filters=[{"column": User.user_type, "value": filter_params.user_type}]
        )

        if filter_params.status:
            two_minutes_ago: datetime = datetime.now(timezone.utc) - timedelta(
                minutes=2
            )
            online_status_filter: ColumnElement[bool] = (
                User.last_online >= two_minutes_ago
                if filter_params.status == "active"
                else or_(User.last_online < two_minutes_ago, User.last_online.is_(None))
            )
            filter_conditions.append(and_(online_status_filter))

        filter_conditions.append(or_(*filters_with_contains))
        filter_conditions.append(and_(*filters_by_equality))

        clients_names: list[str] | None = filter_params.allowed_clients
        users: Sequence = await self.user_repository.get_all(
            sort_clause, filter_conditions, self.session, start, stop, clients_names
        )
        total_user_count: int = await self.user_repository.get_all_users_count(
            filter_conditions, self.session, clients_names
        )

        validated_users: list[dict] = (
            UserListGetSchema(users=users).model_dump().get("users", [{}])
        )
        for user_ in validated_users:
            allowed_clients_result: Sequence[
                UUID
            ] = await self.user_client_repository.get_all_user_clients(
                user_["user_id"], self.session
            )
            user_["allowed_clients"] = allowed_clients_result

        content: list[dict] = await convert_uuids_to_strings(validated_users)

        logger.debug("Get all users executed successfully")
        return content, total_user_count

    async def get_all_for_csv_export(
        self, user_type: str, filter_params: UserListBaseFilterParams
    ) -> str:
        """
        Retrieves a list of users for CSV export with the specified filters and pagination.

        This function fetches a list of users based on the provided filter parameters and permissions, and prepares
        the data for CSV export. Only users with the type "manager" can access the data. It supports filtering by
        user attributes (like email, username, given name, family name, and status), pagination, and sorting. The
        function also includes information about allowed clients for each user. The final data is converted into
        a CSV format for export.

        Args:
            user_type (str): The type of the user making the request. Only a "manager" can access all users.
            filter_params (UserListBaseFilterParams): An object containing the filter parameters for pagination,
                                                    sorting, and other filter conditions.

        Returns:
            str: The CSV formatted data as a string, ready for export.

        Raises:
            HTTPException: If the user does not have the necessary permissions (i.e., not a manager).
        """
        logger.debug("Get all users for csv export started execution")
        if user_type != "manager":
            logger.info(
                "Get all users for csv export failed: user has no enough permissions"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Not enough permissions"
            )

        order_by_column: Column | InstrumentedAttribute[str] = getattr(
            User, filter_params.sort_by, User.email
        )
        sort_clause: UnaryExpression = (
            desc(order_by_column)
            if filter_params.sort_order == "desc"
            else asc(order_by_column)
        )

        filter_conditions: list = []
        filters_with_contains: list[ColumnElement] = await self._add_filter_expressions(
            filters=[
                {"column": User.email, "value": filter_params.name_query},
                {"column": User.username, "value": filter_params.name_query},
                {"column": User.given_name, "value": filter_params.name_query},
                {"column": User.family_name, "value": filter_params.name_query},
            ],
            use_ilike=True,
        )

        filters_by_equality: list[ColumnElement] = await self._add_filter_expressions(
            filters=[{"column": User.user_type, "value": filter_params.user_type}]
        )

        if filter_params.status:
            two_minutes_ago: datetime = datetime.now(timezone.utc) - timedelta(
                minutes=2
            )
            online_status_filter: ColumnElement[bool] = (
                User.last_online >= two_minutes_ago
                if filter_params.status == "active"
                else or_(User.last_online < two_minutes_ago, User.last_online.is_(None))
            )
            filter_conditions.append(and_(online_status_filter))

        filter_conditions.append(or_(*filters_with_contains))
        filter_conditions.append(and_(*filters_by_equality))

        clients_names: list[str] | None = filter_params.allowed_clients
        users: Sequence = await self.user_repository.get_all(
            sort_clause, filter_conditions, self.session, clients_names=clients_names
        )

        validated_users: list[dict] = (
            UserListGetSchema(users=users).model_dump().get("users", [{}])
        )
        for user_ in validated_users:
            allowed_clients_result: Sequence[
                UUID
            ] = await self.user_client_repository.get_all_user_clients(
                user_["user_id"], self.session
            )
            user_["allowed_clients"] = allowed_clients_result

        users_from_schema: list[dict] = await convert_uuids_to_strings(validated_users)
        pandas_dataframe: pd.Dataframe = await self._get_users_csv_dataframe(
            users_from_schema
        )
        logger.debug("Get all users for csv export executed successfully")

        return pandas_dataframe.to_csv(index=False)

    async def add_users(
        self, request: Request, user_type: str, users_list_schema: AddUserListSchema
    ) -> bool:
        """
        Adds a list of users and assigns them to clients, generating confirmation emails.

        This function processes a list of users, checks for unique constraints on email
        and username, creates unconfirmed user records in the database, associates users
        with clients (if provided), and sends them an invitation email with a token to
        confirm their registration. If any email or username duplicates are found, the
        operation is aborted, and an error message is returned.

        Args:
            request (Request): The incoming request object used to extract context.
            user_type (str): The type of the user making the request. Only "manager" users can add users.
            users_list_schema (AddUserListSchema): A schema containing the list of users to be added.

        Returns:
            bool: Returns True if users are added successfully.

        Raises:
            HTTPException:
                - If the user does not have the necessary permissions (not a "manager").
                - If any duplicate email or username is found in the provided list of users.
                - If an invalid client name is provided for a user.
        """
        logger.debug("Add users started execution")

        if user_type != "manager":
            logger.info("Add users failed: user has no enough permissions")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Not enough permissions"
            )

        users_list = users_list_schema.users
        create_user_client_relations_tasks: list[Coroutine] = []
        send_email_tasks: list[Coroutine] = []
        user_not_unique_field_errors: dict = {
            "message": "Some users have non-unique fields",
            "errors": {"non_unique_usernames": [], "non_unique_emails": []},
        }
        # If this field is True, no db operations will be called,
        # just check trough iteration for duplicates
        any_unique_error: bool = False
        used_in_bulk_invite_emails: list[str] = []
        used_in_bulk_invite_usernames: list[str] = []

        for line_index, user_object in enumerate(users_list):
            unique_check_result: bool = await self._check_email_and_username_in_use(
                user_not_unique_field_errors,
                line_index,
                user_object.email,
                user_object.username,
                user_object.email in used_in_bulk_invite_emails,
                user_object.username in used_in_bulk_invite_usernames,
            )
            if unique_check_result:
                any_unique_error = True

            if not any_unique_error:
                # user will set own password on confirmation of registration
                # this password used as stub
                random_password: str = secrets.token_urlsafe(16)
                hashed_stub_password: str = (
                    await self.password_hash_service.get_hashed_value(random_password)
                )

                used_in_bulk_invite_emails.append(user_object.email)
                used_in_bulk_invite_usernames.append(user_object.username)

                user_: User = await self.user_repository.create_unconfirmed_user(
                    user_object.username,
                    user_object.email,
                    user_object.given_name,
                    user_object.family_name,
                    user_object.user_type,
                    hashed_stub_password,
                    self.session,
                )

                if user_object.allowed_clients:
                    for client_name in user_object.allowed_clients:
                        client_object = await self.client_repository.get_by_name(
                            client_name, self.session
                        )
                        if not client_object:
                            logger.info(
                                f"Add users failed: invalid client with "
                                f"name {client_name} in line {line_index + 1}"
                            )
                            raise HTTPException(
                                status_code=status.HTTP_400_BAD_REQUEST,
                                detail=f"Invalid client with name {client_name} in line {line_index + 1}",
                            )

                        create_user_client_relations_tasks.append(
                            self.user_client_repository.create_user_client(
                                user_.user_id, client_object.client_id, self.session
                            )
                        )

                data_to_encode: dict[str, str | None] = {
                    "username": user_object.username,
                    "user_type": user_object.user_type,
                }
                fernet_mail_token: str = (
                    await self.auth_token_service.create_auth_token(
                        data=data_to_encode, expires_delta=timedelta(days=1)
                    )
                )

                email_data: dict = {
                    "username": user_object.username,
                    "invite_link": f"{Config.FRONTEND_URL}/invite/{fernet_mail_token}",
                }
                send_email_tasks.append(
                    self.email_notification_service.send(
                        email_data,
                        "invite_mail.html",
                        request.state.aio_session,
                        user_object.email,
                        "Invite to Record Ranger",
                    )
                )

        if any_unique_error:
            await self.session.rollback()
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=user_not_unique_field_errors,
            )

        await asyncio.gather(*create_user_client_relations_tasks)
        await commit_session(self.session)
        await asyncio.gather(*send_email_tasks)
        logger.debug("Add users executed successfully")

        return True

    async def add_users_csv(
        self, request: Request, user_type: str, csv_file: UploadFile
    ) -> bool:
        """
        Adds users from a CSV file, assigns them to clients, and sends them invitation emails.

        This function processes a CSV file containing user details, checks for unique constraints
        on email and username, creates unconfirmed user records in the database, associates users
        with clients (if provided), and sends them an invitation email with a token to confirm
        their registration. If any email or username duplicates are found, the operation is aborted,
        and an error message is returned. Only users with the "manager" user type can perform this action.

        Args:
            request (Request): The incoming request object used to extract context.
            user_type (str): The type of the user making the request. Only "manager" users can add users.
            csv_file (UploadFile): The CSV file containing the user details to be added.

        Returns:
            bool: Returns True if users are added successfully.

        Raises:
            HTTPException:
                - If the user does not have the necessary permissions (not a "manager").
                - If the file type is not "text/csv".
                - If any duplicate email or username is found in the CSV file.
                - If an invalid client name is provided for a user.
                - If the CSV file contains invalid user data (validation error).
        """
        logger.debug("Add users csv started execution")

        if user_type != "manager":
            logger.info("Add users csv failed: user has no enough permissions")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Not enough permissions"
            )

        if csv_file.content_type != "text/csv":
            logger.info("Add users csv failed: invalid document type")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid document type"
            )

        validated_csv_file: UploadFile = await validate_file_size(csv_file, 1)

        create_user_client_relations_tasks: list[Coroutine] = []
        send_email_tasks: list[Coroutine] = []

        user_not_unique_field_errors: dict = {
            "message": "Some users have non-unique fields",
            "errors": {"non_unique_usernames": [], "non_unique_emails": []},
        }

        any_unique_error: bool = False
        used_in_bulk_invite_emails: list[str] = []
        used_in_bulk_invite_usernames: list[str] = []

        async for line_idx, row in self.csv_service.csv_read_lines(validated_csv_file):
            if row.get("allowed_clients"):
                row["allowed_clients"] = row["allowed_clients"].split(";")

            try:
                user_info: dict[str, Any] = AddUserSchema(**row).model_dump(
                    exclude_none=True
                )
            except ValidationError as e:
                logger.info(f"Add users csv failed: {e.errors()}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=jsonable_encoder(e.errors()),
                )

            unique_check_result: bool = await self._check_email_and_username_in_use(
                user_not_unique_field_errors,
                line_idx,
                user_info["email"],
                user_info["username"],
                user_info["email"] in used_in_bulk_invite_emails,
                user_info["username"] in used_in_bulk_invite_usernames,
            )

            if unique_check_result:
                any_unique_error = True

            if not any_unique_error:
                random_password: str = secrets.token_urlsafe(16)
                hashed_stub_password: str = (
                    await self.password_hash_service.get_hashed_value(random_password)
                )

                used_in_bulk_invite_emails.append(user_info["email"])
                used_in_bulk_invite_usernames.append(user_info["username"])

                user_: User = await self.user_repository.create_unconfirmed_user(
                    user_info["username"],
                    user_info["email"],
                    user_info["given_name"],
                    user_info["family_name"],
                    user_info["user_type"],
                    hashed_stub_password,
                    self.session,
                )

                if user_info.get("allowed_clients"):
                    for client_name in user_info.get("allowed_clients", []):
                        client_object = await self.client_repository.get_by_name(
                            client_name, self.session
                        )
                        if not client_object:
                            logger.info(
                                f"Add users csv failed: invalid client "
                                f"with name {client_name} in line {line_idx + 1}"
                            )
                            raise HTTPException(
                                status_code=status.HTTP_400_BAD_REQUEST,
                                detail=f"Invalid client with name {client_name} in line {line_idx + 1}",
                            )

                        create_user_client_relations_tasks.append(
                            self.user_client_repository.create_user_client(
                                user_.user_id, client_object.client_id, self.session
                            )
                        )
                data_to_encode: dict[str, str] = {
                    "username": user_info["username"],
                    "user_type": user_info["user_type"],
                }

                fernet_mail_token: str = (
                    await self.auth_token_service.create_auth_token(
                        data=data_to_encode, expires_delta=timedelta(days=1)
                    )
                )

                email_data: dict = {
                    "username": user_info["username"],
                    "invite_link": f"{Config.FRONTEND_URL}/invite/{fernet_mail_token}",
                }

                send_email_tasks.append(
                    self.email_notification_service.send(
                        email_data,
                        "invite_mail.html",
                        request.state.aio_session,
                        user_info["email"],
                        "Invite to Record Ranger",
                    )
                )

        if any_unique_error:
            await self.session.rollback()
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=user_not_unique_field_errors,
            )

        await asyncio.gather(*create_user_client_relations_tasks)
        await commit_session(self.session)
        await asyncio.gather(*send_email_tasks)

        logger.debug("Add users csv executed successfully")
        return True

    async def get_unconfirmed_user(
        self, form_data: GetUnconfirmedUserFormSchema
    ) -> dict:
        """
        Retrieves the data of an unconfirmed user based on an invite token.

        This function decodes an invite token to extract the username, retrieves the user from
        the database, and returns the user's data in a specific schema format. If the user cannot
        be found or if the token is invalid, an error is raised.

        Args:
            form_data (GetUnconfirmedUserFormSchema): The schema containing the invite token to decode.

        Returns:
            dict: A dictionary containing the user's data, validated and serialized according to
                the `UnconfirmedUserSchema`.

        Raises:
            HTTPException:
                - If the token is invalid or cannot be decoded.
                - If the user does not exist in the database.
        """
        logger.debug("Get unconfirmed user started execution")
        payload: dict = await self.auth_token_service.decode_auth_token(
            form_data.invite_token
        )
        payload_username: str = payload.get("username", "")

        user: UserListOneUser | None = await self.get_user(payload_username)
        if user is None:
            logger.info("Get unconfirmed user failed: could not find user")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Could not find user"
            )

        user_data: dict = UnconfirmedUserSchema.model_validate(
            user.model_dump()
        ).model_dump()
        logger.debug("Get unconfirmed user executed successfully")
        return user_data

    async def update_user(
        self,
        request: Request,
        user_id: UUID,
        user_type: str,
        update_user_info: UpdateUserSchema,
        update_user_id: UUID,
    ) -> User:
        """
        Updates the information of an existing user.

        This function allows a manager to update a user's information, including email, username,
        user role, and allowed clients. The function performs several validations, such as ensuring
        the user exists, checking for unique email and username, and ensuring the user does not
        already have the role they are being assigned. It also logs any changes made to the user's
        fields and sends an email notification upon successful update.

        Args:
            request (Request): The FastAPI request object.
            user_id (UUID): The ID of the manager requesting the update.
            user_type (str): The role of the manager (should be "manager").
            update_user_info (UpdateUserSchema): The schema containing the updated user information.
            update_user_id (UUID): The ID of the user to be updated.

        Returns:
            User: The updated user object.

        Raises:
            HTTPException:
                - If the manager does not have enough permissions.
                - If the user to update does not exist.
                - If the user already has the role they are being assigned.
                - If the email or username already exists.
                - If any invalid client names are provided.
                - If there is an integrity error while updating the user.
        """
        logger.debug("Update user started execution")
        fields_to_update: dict = update_user_info.model_dump(
            exclude_unset=True, exclude_none=True
        )
        update_user_tasks: list[Coroutine] = []

        if user_type != "manager":
            logger.info("Update user failed: user has no enough permissions")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Not enough permissions"
            )
        user_to_change: User | None = await self.user_repository.get_by_id(
            update_user_id, self.session
        )
        if user_to_change is None:
            logger.info(f"Update user failed: user with id {update_user_id} not found")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User with this id not found",
            )
        if user_to_change.user_type == fields_to_update.get("user_type"):
            logger.info(
                f"Update user failed: user with "
                f"id {update_user_id} already has this role"
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User already has this role",
            )

        if update_user_info.email and await self.user_repository.get_by_email(
            update_user_info.email, self.session
        ):
            logger.info(
                f"Update user failed: user with email "
                f"{update_user_info.email} already exist"
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User with this email already exist",
            )

        if update_user_info.username and await self.user_repository.get_by_username(
            update_user_info.username, self.session
        ):
            logger.info(
                f"Update user failed: user with username "
                f"{update_user_info.username} already exist"
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User with this username already exist",
            )

        if fields_to_update.get("allowed_clients"):
            # delete other to bulk create with gather
            await self.user_client_repository.delete_user_clients(
                user_to_change.user_id, self.session
            )

            user_clients: list[str] = fields_to_update.pop("allowed_clients")
            update_user_tasks: list[Coroutine] = []

            for client_name in user_clients:
                client_object = await self.client_repository.get_by_name(
                    client_name, self.session
                )
                if not client_object:
                    logger.info(
                        f"Update user failed: invalid client with name {client_name}"
                    )
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Invalid client with name {client_name}",
                    )

                update_user_tasks.append(
                    self.user_client_repository.create_user_client(
                        user_to_change.user_id, client_object.client_id, self.session
                    )
                )

        update_user_tasks.append(
            self.user_repository.update_fields_without_commit(
                user_to_change.user_id, self.session, **fields_to_update
            )
        )

        for field_name in fields_to_update:
            action: str = f"{field_name}_change"
            previous_value = getattr(user_to_change, field_name)
            new_value = fields_to_update.get(field_name)

            await self.user_activity_logs_repository.create_user_activity_log(
                {
                    "user_id": user_id,
                    "action": action,
                    "description": f"user {user_to_change.user_id} from {previous_value} to {new_value}",
                },
                self.session,
            )

        try:
            await asyncio.gather(*update_user_tasks)
            await commit_session(self.session)

            data: dict = {
                "username": user_to_change.username,
                "link": Config.FRONTEND_URL,
            }
            await self.email_notification_service.send(
                data,
                "update_user_mail.html",
                request.state.aio_session,
                user_to_change.email,
                "Update in Record Ranger",
            )

        except IntegrityError:
            logger.info("Update user failed: user was not updated due invalid values")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User was not updated due invalid values",
            )

        logger.debug("Update user executed successfully")
        return user_to_change

    async def delete_user(
        self, request: Request, user_id: UUID, user_type: str, delete_user_id: UUID
    ) -> bool:
        """
        Deletes an existing user from the system.

        This function allows a manager to delete a user. The function verifies that the user exists,
        checks if the requester has sufficient permissions (i.e., the user is a manager), and logs
        the action. Upon successful deletion, an email notification is sent to the user, and their
        account deletion is recorded in the activity logs.

        Args:
            request (Request): The FastAPI request object.
            user_id (UUID): The ID of the manager requesting the deletion.
            user_type (str): The role of the manager (should be "manager").
            delete_user_id (UUID): The ID of the user to be deleted.

        Returns:
            bool: True if the user was successfully deleted, False otherwise.

        Raises:
            HTTPException:
                - If the user to delete does not exist.
                - If the requester does not have sufficient permissions.
                - If an error occurs during the deletion process.
        """
        logger.debug("Delete user started execution")
        user_: User | None = await self.user_repository.get_by_id(
            delete_user_id, self.session
        )
        if user_ is None:
            logger.info(
                f"Delete user failed: user with " f"id {delete_user_id} not found"
            )
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User with this id not found",
            )

        if user_type != "manager":
            logger.info(
                f"Delete user failed: user with "
                f"id {user_id} has no enough permissions"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Not enough permissions"
            )

        await self.chunk_service.change_chunk_status(user_id, "account delete")

        success: bool = await self.user_repository.delete_user(
            delete_user_id, self.session
        )
        if success:
            data: dict = {
                "username": user_.username,
                "support_link": Config.FRONTEND_URL,
            }

            await self.user_activity_logs_repository.create_user_activity_log(
                {
                    "user_id": user_id,
                    "action": "user_delete",
                    "description": "successful",
                },
                self.session,
            )

            await self.email_notification_service.send(
                data,
                "delete_user_mail.html",
                request.state.aio_session,
                user_.email,
                "Account Deletion Notice from Record Ranger",
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="User was not updated",
            )

        logger.debug("Delete user executed successfully")
        return success

    async def reset_password(self, request: Request, email: str) -> bool:
        """
        Initiates the password reset process for a user by sending a reset link to their email.

        This function generates a password reset token for the user identified by their email,
        creates a reset link, and sends it via email. The token expires in 15 minutes. If the
        user is not found, a 404 error is raised.

        Args:
            request (Request): The FastAPI request object.
            email (str): The email address of the user requesting a password reset.

        Returns:
            bool: True if the password reset process was initiated successfully.

        Raises:
            HTTPException:
                - If no user is found with the provided email.
                - If an error occurs while sending the reset email.
        """
        # TODO: make this logic only for email, username needed to be checked
        # because of useless logic on frontend before the release
        logger.debug(f"Reset password for email '{email}' started execution")

        user_: UserListOneUser | None = await self.get_user(email)
        if user_ is None:
            logger.info(
                f"Reset password for email '{email}' failed: "
                f"user with this email not found"
            )
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User with this email not found",
            )

        data_to_encode: dict[str, str] = {
            "reset_username": user_.username,
            "sub": user_.username,
        }
        confirmation_token: str = await self.auth_token_service.create_auth_token(
            data=data_to_encode, expires_delta=timedelta(minutes=15)
        )

        data: dict = {
            "username": user_.username,
            "link": f"{Config.FRONTEND_URL}/confirm_reset/{confirmation_token}",
        }
        await self.email_notification_service.send(
            data,
            "reset_password_user.html",
            request.state.aio_session,
            user_.email,
            "Password Reset",
        )
        logger.debug(f"Reset password for email '{email}' executed successfully")
        return True

    async def confirm_reset_password(
        self, confirmation_token: str, new_password: str
    ) -> bool:
        """
        Confirms the password reset process and updates the user's password.

        This function decodes the password reset token, verifies the user's identity,
        validates the new password, hashes it, and updates the user's password in the
        database. If the token is invalid or expired, a 401 error is raised.

        Args:
            confirmation_token (str): The password reset token sent to the user.
            new_password (str): The new password the user wants to set.

        Returns:
            bool: True if the password was successfully reset.

        Raises:
            HTTPException:
                - If the token is invalid or expired.
                - If the password validation fails.
                - If an error occurs during the update process.
        """
        logger.debug("Confirm reset password started execution")
        payload = await self.auth_token_service.decode_auth_token(confirmation_token)
        user: UserListOneUser | None = await self.get_user(
            payload.get("reset_username", "")
        )
        if not payload or not user:
            logger.info("Confirm reset password failed: incorrect token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect token",
                headers={"WWW-Authenticate": "Bearer"},
            )
        try:
            validated_password: str = validate_password(
                new_password,
                await self.password_hash_service.verify(new_password, user.password),
            )
        except ValueError as ve:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(ve))
        hashed_password: str = await self.password_hash_service.get_hashed_value(
            validated_password
        )
        await self.user_repository.update_fields(
            user.user_id,
            self.session,
            **{"password": hashed_password, "login_attempt": 0, "active": True},
        )
        logger.debug("Confirm reset password executed successfully")
        return True
