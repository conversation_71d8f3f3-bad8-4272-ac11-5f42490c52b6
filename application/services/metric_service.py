from typing import Literal, Protocol, Sequence

from sqlalchemy import <PERSON><PERSON><PERSON>ping

from domain.repositories import PacketRepository
from infrastructure.database.repositories.sqlalchemy import SQLAlchemyPacketRepository
from infrastructure.http_clients import PrometheusHTTPClient, PrometheusHTTPClientImpl
from infrastructure.logs import get_logger


logger = get_logger(__name__)


class MetricService(Protocol):
    async def send_packet_status_metric(
        self,
        metric_name: str,
        metric_help: str,
        status: Literal["in_system", "out_system"],
    ) -> None: ...


class PrometheusMetricService(MetricService):
    def __init__(self, application: str):
        self.application: str = application
        self.http_client: PrometheusHTTPClient = PrometheusHTTPClientImpl
        self.packet_repository: PacketRepository = SQLAlchemyPacketRepository()

    async def send_packet_status_metric(
        self,
        metric_name: str,
        metric_help: str,
        status: Literal["in_system", "out_system"],
    ) -> None:
        """
        Sends a metric about the packet status to a monitoring service.

        This function retrieves the total count of packets based on their status
        (`in_system` or `out_system`), and sends this data as a metric to the
        configured monitoring service using an HTTP client.

        Args:
            metric_name (str): The name of the metric to be sent.
            metric_help (str): A description of the metric for monitoring purposes.
            status (Literal["in_system", "out_system"]): The status of the packets to count.
                "in_system" refers to packets currently in the system,
                while "out_system" refers to packets that have been processed or moved out of the system.

        Returns:
            None.

        Raises:
            HTTPException: If sending the metric fails or encounters an error in the process.
        """
        logger.debug(
            f"Send packet status metric started " f"execution for metric: {metric_name}"
        )

        packets: Sequence[
            RowMapping
        ] = await self.packet_repository.get_packets_by_processed_status(status)
        packets_total_count: int = len(packets)

        base_dict: dict = {
            "application": self.application,
            "type": "gauge",
            "additional_parameters": {"type": "web"},
        }

        # await self.http_client.send_metric(
        #     name=metric_name,
        #     value=float(packets_total_count),
        #     help_=metric_help,
        #     base_dict=base_dict,
        # )
        logger.debug(
            f"Send packet status metric executed "
            f"successfully for metric: {metric_name}"
        )
        logger.info(f"Metric {metric_name} sent successfully.")

    async def send_packet_status_metric(
        self,
        metric_name: str,
        metric_help: str,
        status: str,
        metric_type: str = "gauge",
    ) -> None:
        """
        Sends a Prometheus metric related to the status of packets.

        Args:
            metric_name: The name of the metric (e.g., 'packets_in_system').
            metric_help: A help string for the metric.
            status: The status of the packets (e.g., 'in_system', 'out_system').
            metric_type: The type of metric ('gauge' by default).
        """
        # packet_repository: PacketRepository = SQLAlchemyPacketRepository()
        # metric_value: int = await packet_repository.get_packet_count_by_status(
        #     status=status
        # )
        # base_dict: dict = {
        #     "application": self.application,
        #     "type": metric_type,
        #     "additional_parameters": {"type": "status"},
        # }
        # await self.http_client.send_metric(
        #     name=metric_name, value=metric_value, help_=metric_help, base_dict=base_dict
        # )
        logger.info(f"Metric {metric_name} for status {status} sent successfully.")
