import { ThemeProvider, createTheme } from "@mui/material";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import { Provider } from "react-redux";
import { RouterProvider } from "react-router-dom";
import { DelegatedProvider } from "./components/DelegatedPackages/DelegatedContext.tsx";
import { AuthContextProvider } from "./components/Router/Auth/AuthContext.tsx";
import { AppRouter } from "./components/Router/Router.tsx";
import { ScreenshotContextProvider } from "./components/ScreenshotContext/ScreenshotContext.tsx";
import { ScreenshotOverlay } from "./components/Widgets/ScreenshotOverlay.tsx";
import { AppWSProvider } from "./components/WSContext/WSContext.tsx";
import store from "./store/store";
import { setupAxiosInterceptors } from "./api/axiosConfig.ts";
import { useAuthContext } from "./hooks/useAuth.ts";
import { useEffect } from "react";
import { TokenMonitor } from "./components/TokenMonitor/TokenMonitor";

const colors = {
  background: "#607274",
  primary: "#FAEED1",
  secondary: "#03DAC6",
  text: "#FFFFFF",
  border: "#373737",
};

const darkTheme = createTheme({
  palette: {
    mode: "dark",
    primary: {
      main: colors.primary,
    },
    secondary: {
      main: colors.secondary,
    },
    background: {
      default: colors.background,
      paper: colors.background,
    },
    text: {
      primary: colors.text,
      secondary: colors.text,
    },
    action: {
      active: colors.text,
    },
    divider: colors.border,
  },
});

const AxiosInterceptorSetup = () => {
  const { logout } = useAuthContext();
  
  useEffect(() => {
    setupAxiosInterceptors(logout);
  }, [logout]);
  
  return null;
};

const PdfViewerPage = () => {
  return (
    <Provider store={store}>
      <ThemeProvider theme={darkTheme}>
        <AuthContextProvider>
          <TokenMonitor />
          <AxiosInterceptorSetup />
          <AppWSProvider>
            <ScreenshotContextProvider>
              <DelegatedProvider>
                <ScreenshotOverlay>
                  <RouterProvider router={AppRouter} />
                </ScreenshotOverlay>
              </DelegatedProvider>
            </ScreenshotContextProvider>
          </AppWSProvider>
        </AuthContextProvider>
      </ThemeProvider>
    </Provider>
  );
};

export default PdfViewerPage;
