import { useCallback, useContext, useState, useEffect } from "react";
import { AuthContext } from "../components/Router/Auth/AuthContext.tsx";
import { TokenResponse } from "../store/queries/users/types/User.ts";

export interface UserAuth {
  token: string | null;
  refresh: string | null;
  date: string | null;
  role: string | null;
  user_id: string | null;
  username: string | null;
  validated: string | null;
  twoFactorPassed: boolean;
  login: (token: TokenResponse) => void;
  logout: () => void;
  pass_two_factor: () => void;
  getAuthData: () => {
    token: string | null;
    refresh: string | null;
    date: string | null;
    role: string | null;
    user_id: string | null;
    validated: string | null;
  };
}

export const useAuthContext = () => {
  return useContext(AuthContext);
};

export const useAuth = (): UserAuth => {
  const [StoreAuthData, setStoreAuthData] = useState(
    JSON.parse(localStorage.getItem("userAuth")),
  );
  const [twoFactorPassed, setTwoFactorPassed] = useState(
    Boolean(localStorage.getItem("twoFactor")),
  );

  useEffect(() => {
    const handleStorageChange = () => {
      const newAuthData = JSON.parse(localStorage.getItem("userAuth"));
      setStoreAuthData(newAuthData);
    };
    
    // Listen for storage events (from other tabs)
    window.addEventListener("storage", handleStorageChange);
    
    // Listen for custom event (from same tab)
    window.addEventListener("authTokenUpdated", handleStorageChange);
    
    return () => {
      window.removeEventListener("storage", handleStorageChange);
      window.removeEventListener("authTokenUpdated", handleStorageChange);
    };
  }, []);

  const login = useCallback((data: TokenResponse) => {
    setStoreAuthData(data);
    localStorage.setItem("userAuth", JSON.stringify(data));
    window.dispatchEvent(new Event("storage"));
  }, []);

  const logout = useCallback(() => {
    localStorage.clear();
    setStoreAuthData(null);
    setTwoFactorPassed(false);
    window.dispatchEvent(new Event("storage"));
  }, []);

  const pass_two_factor = useCallback(() => {
    setTwoFactorPassed(true);
    localStorage.setItem("twoFactor", "passed");
  }, []);
  const getAuthData = useCallback(
    () => JSON.parse(localStorage.getItem("userAuth")),
    [],
  );

  if (!StoreAuthData)
    return {
      token: null,
      refresh: null,
      date: null,
      role: null,
      user_id: null,
      validated: null,
      username: null,
      twoFactorPassed,
      login,
      logout,
      pass_two_factor,
      getAuthData,
    };

  const { token, refresh, date, role, user_id, validated, username } =
    StoreAuthData;
  return {
    token,
    refresh,
    date,
    role,
    user_id,
    validated,
    twoFactorPassed,
    logout,
    pass_two_factor,
    getAuthData,
    username,
    login,
  };
};
