import { ClickAwayListener, Divider, Paper } from "@mui/material";
import { FC, useCallback, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import { useSearchParams } from "react-router-dom";
import { StyleCreator, useStyleCreator } from "../../hooks/useStyleCreator.ts";
import { useChunkUpdate } from "../../store/queries/chunks/chunks.query.ts";
import { ChunkToUpdate } from "../../store/queries/chunks/types/UpdateChunk.type.ts";
import { useAppSelector } from "../../store/store.ts";
import { SegmentLoadingOverlay } from "./SegmentConstructions/SegmentLoadingOverlay.tsx";
import { SegmentDrawer } from "./SegmentDrawer/SegmentDrawer.tsx";
import { SegmentForm } from "./SegmentForm.tsx";
import { SegmentFormSubmit } from "./SegmentFormSubmit.tsx";

const styles: StyleCreator<"container"> = () => ({
  container: {
    height: "calc(100vh - 20px)",
    border: "1px solid white",
    borderRadius: "8px",
    padding: "8px",
    display: "flex",
    flexDirection: "column",
    gap: "18px",
    position: "relative",
    overflow: "hidden",
  },
});

interface Props {
  onBack: () => void;
  onSuccess: () => void;
  fieldsToValidate: string[];
}

export const SegmentEditor: FC<Props> = ({
  onBack,
  onSuccess,
  fieldsToValidate,
}) => {
  const c = useStyleCreator(styles);

  const [update, { isLoading }] = useChunkUpdate();

  const metadata = useAppSelector((state) => state.data.metadata);
  const slices = useAppSelector((state) => state.data.slices);
  const comment = useAppSelector((state) => state.data.comment);
  const status = useSelector((state: any) => state.data.status);

  const [open, setOpen] = useState(false);
  const [sliceToShow, setSliceToShow] = useState(() => {
    return slices.findIndex(
      ({ incomplete, duplicate_of }) => !incomplete && !duplicate_of,
    );
  });
  const [search, setSearch] = useSearchParams();

  const Form = useForm<ChunkToUpdate>({
    defaultValues: {
      comment,
      metadata,
      predictions: slices,
    },
  });

  const onSubmit = useCallback(
    ({ to_delegate, ...payload }: ChunkToUpdate & { to_delegate: boolean }) => {
      const package_id = localStorage.getItem("activeFile");

      update({
        package_id,
        payload,
        update_type: to_delegate ? "delegate" : "submit",
      })
        .unwrap()
        .then(() => {
          onBack();
          onSuccess();

          search.set("from", status);
          setSearch(search);
        })
        .catch((error) => {
          toast.error(
            `Error occurred. Status: ${error?.message ?? "unknown"}`,
            { position: "bottom-right" },
          );
        });
    },
    [onBack, onSuccess, search, setSearch, status, update],
  );

  const handleReset = useCallback(() => Form.reset(), [Form]);

  return (
    <FormProvider {...Form}>
      <ClickAwayListener
        onClickAway={() => {
          if (open) setOpen(false);
        }}
      >
        <Paper
          component={"form"}
          onSubmit={Form.handleSubmit(onSubmit)}
          onReset={() => handleReset()}
          sx={c.container}
        >
          <SegmentDrawer
            slices={slices}
            open={open}
            setOpen={setOpen}
            sliceToShow={sliceToShow}
            setSliceToShow={setSliceToShow}
          />
          <SegmentForm
            sliceToShow={sliceToShow}
            setSliceToShow={setSliceToShow}
            setOpen={setOpen}
            fieldsToValidate={fieldsToValidate}
          />
          <Divider />
          <SegmentFormSubmit onBack={onBack} loading={isLoading} />
          {isLoading && <SegmentLoadingOverlay />}
        </Paper>
      </ClickAwayListener>
    </FormProvider>
  );
};
