import UndoIcon from "@mui/icons-material/Undo";
import { Box, Button, TextField, Tooltip, Typography } from "@mui/material";
import { FC, useCallback, useMemo } from "react";
import { Controller, useFormContext } from "react-hook-form";
import {
  StyleCreator,
  useStyleCreator,
} from "../../../hooks/useStyleCreator.ts";
import { ExtractTextButton } from "../SegmentConstructions/ExtractTextButton.tsx";
import { SegmentFieldValidator } from "../SegmentConstructions/SegmentFieldValidator.tsx";
import { FieldProps, PackageToUpdate } from "../segment.types.ts";
import { isConstructedField } from "./utils/constructed.utils.ts";
import {
  addMark,
  addTextDecoration,
  getColorByRequired,
} from "./utils/fields.utils.ts";
import { getTextFieldChangeHandler } from "./utils/text.utils.ts";

const styles: StyleCreator<"container" | "button" | "input"> = () => ({
  container: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    gap: "24px",
  },
  input: {
    width: "100%",
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    gap: "8px",
  },
  button: {
    height: "40px",
    width: "40px",
    minWidth: "40px",
  },
});

export const SegmentTextRow: FC<FieldProps> = ({
  index,
  base,
  name,
  label,
  level = 0,
  validation,
}) => {
  const c = useStyleCreator(styles);
  const fieldKey = useMemo(() => `${base}.${name}.value`, [base, name]);
  const { setValue, control } = useFormContext();

  const { resetField } = useFormContext<PackageToUpdate>();
  const handleReset = useCallback(
    () => resetField(fieldKey as "metadata"),
    [fieldKey, resetField],
  );

  return (
    <Controller
      control={control}
      name={fieldKey}
      render={({ field: { value, onChange, ref }, fieldState }) => {
        return (
          <Box sx={c.container}>
            <SegmentFieldValidator
              validation={validation}
              fieldKey={fieldKey}
              value={value}
            />
            <Typography
              textTransform={"capitalize"}
              style={{
                minWidth: "35%",
                paddingLeft: `${level * 40}px`,
                color: getColorByRequired(fieldKey),
                textDecoration: addTextDecoration(fieldKey),
              }}
              fontWeight={value ? 500 : 700}
            >
              {label}
              {addMark(fieldKey)}:
            </Typography>
            <Box sx={c.input}>
              <Tooltip title={fieldState.error?.message ?? null}>
                <TextField
                  autoComplete={"off"}
                  type={"text"}
                  value={value === null ? "" : value}
                  onChange={(event) =>
                    getTextFieldChangeHandler(
                      index,
                      fieldKey,
                      onChange,
                      setValue,
                      event.target.value,
                    )
                  }
                  ref={ref}
                  size={"small"}
                  fullWidth
                  error={fieldState.invalid}
                  placeholder={`Enter ${label}`}
                  InputProps={{
                    readOnly: isConstructedField(fieldKey),
                  }}
                  helperText={isConstructedField(fieldKey) ? "readonly" : null}
                  sx={{
                    "& .MuiFormHelperText-root": {
                      width: "fit-content",
                      margin: "0",
                      position: "absolute",
                      bottom: 0,
                      right: "10px",
                    },
                  }}
                />
              </Tooltip>
              <ExtractTextButton onChange={onChange} fieldKey={fieldKey} />
              <Button
                tabIndex={-1}
                sx={c.button}
                variant={"outlined"}
                onClick={handleReset}
              >
                <UndoIcon />
              </Button>
            </Box>
          </Box>
        );
      }}
    />
  );
};
