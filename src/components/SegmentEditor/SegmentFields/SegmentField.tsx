import { FC, useMemo } from "react";
import { SegmentBooleanField } from "./SegmentBooleanField.tsx";
import { SegmentDateField } from "./SegmentDateField.tsx";
import { SegmentDropdownField } from "./SegmentDropdownField.tsx";
import { SegmentGenderField } from "./SegmentGenderField.tsx";
import { SegmentPhoneField } from "./SegmentPhoneField.tsx";
import { SegmentQuantityField } from "./SegmentQuantityField.tsx";
import { SegmentSnnField } from "./SegmentSnnField.tsx";
import { SegmentStateInput } from "./SegmentStateField.tsx";
import { SegmentTextArea } from "./SegmentTextArea.tsx";
import { SegmentTextRow } from "./SegmentTextRow.tsx";
import { SegmentZipField } from "./SegmentZipField.tsx";
import {
  isBooleanField,
  isDateField,
  isDropdown<PERSON>ield,
  isG<PERSON><PERSON>ield,
  is<PERSON><PERSON><PERSON><PERSON>,
  is<PERSON>uant<PERSON><PERSON><PERSON>,
  isSnn<PERSON>ield,
  isState<PERSON>ield,
  isS<PERSON><PERSON>ield,
  isSum<PERSON><PERSON><PERSON>ield,
  isZ<PERSON><PERSON>ield,
} from "./utils/fields.utils.ts";
import { getValidationSchema } from "./utils/getValidationSchema.ts";

interface Props {
  index?: number;
  base: string;
  name: string;
  label: string;
  level?: number;
  treatment?: boolean;
}

export const SegmentField: FC<Props> = (props) => {
  const key = useMemo(() => `${props.base}.${props.name}`, [props]);

  const validationSchema = useMemo(() => getValidationSchema(key), [key]);

  const Field = useMemo(() => {
    switch (true) {
      case isStringField(key):
        return SegmentTextRow;
      case isBooleanField(key):
        return SegmentBooleanField;
      case isDateField(key):
        return SegmentDateField;
      case isStateField(key):
        return SegmentStateInput;
      case isPhoneField(key):
        return SegmentPhoneField;
      case isZipField(key):
        return SegmentZipField;
      case isGenderField(key):
        return SegmentGenderField;
      case isSnnField(key):
        return SegmentSnnField;
      case isQuantityField(key):
        return SegmentQuantityField;
      case isDropdownField(key):
        return SegmentDropdownField;
      case props.treatment || isSummaryField(key):
        return SegmentTextArea;
      default:
        return SegmentTextRow;
    }
  }, [key, props.treatment]);

  return <Field key={key} validation={validationSchema} {...props} />;
};
