import UndoIcon from "@mui/icons-material/Undo";
import { Box, Button, TextField, Tooltip, Typography } from "@mui/material";
import { FC, useCallback, useMemo } from "react";
import { Controller, useFormContext } from "react-hook-form";
import {
  StyleCreator,
  useStyleCreator,
} from "../../../hooks/useStyleCreator.ts";
import { ExtractTextButton } from "../SegmentConstructions/ExtractTextButton.tsx";
import { SegmentFieldValidator } from "../SegmentConstructions/SegmentFieldValidator.tsx";
import { FieldProps, PackageToUpdate } from "../segment.types.ts";
import {
  addMark,
  addTextDecoration,
  getColorByRequired,
} from "./utils/fields.utils.ts";

const styles: StyleCreator<
  "container" | "button" | "input" | "buttons"
> = () => ({
  container: {
    display: "flex",
    alignItems: "start",
    justifyContent: "space-between",
    gap: "24px",
  },
  input: {
    width: "100%",
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    gap: "8px",
  },
  buttons: {
    display: "flex",
    flexDirection: "column",
    gap: "6px",
  },
  button: {
    height: "40px",
    width: "40px",
    minWidth: "40px",
  },
});

export const SegmentTextArea: FC<FieldProps> = ({
  base,
  name,
  label,
  level = 0,
  validation,
}) => {
  const c = useStyleCreator(styles);
  const fieldKey = useMemo(() => `${base}.${name}.value`, [base, name]);

  const { resetField } = useFormContext<PackageToUpdate>();
  const handleReset = useCallback(
    () => resetField(fieldKey as "metadata"),
    [fieldKey, resetField],
  );

  return (
    <Controller
      name={fieldKey}
      render={({ field: { value, onChange, ref }, fieldState }) => {
        return (
          <Box sx={c.container}>
            <SegmentFieldValidator
              validation={validation}
              fieldKey={fieldKey}
              value={value}
            />
            <Typography
              textTransform={"capitalize"}
              style={{
                minWidth: "35%",
                paddingLeft: `${level * 40}px`,
                color: getColorByRequired(fieldKey),
                textDecoration: addTextDecoration(fieldKey),
                marginTop: "8px",
              }}
              fontWeight={value ? 500 : 700}
            >
              {label}
              {addMark(fieldKey)}:
            </Typography>
            <Box sx={c.input}>
              <Tooltip title={fieldState.error?.message ?? null}>
                <TextField
                  autoComplete={"off"}
                  type={"text"}
                  value={value === null ? "" : value}
                  onChange={onChange}
                  ref={ref}
                  size={"small"}
                  fullWidth
                  error={fieldState.invalid}
                  placeholder={`Enter ${label}`}
                  multiline
                  rows={3}
                />
              </Tooltip>
              <Box sx={c.buttons}>
                <ExtractTextButton onChange={onChange} fieldKey={fieldKey} />
                <Button
                  tabIndex={-1}
                  sx={c.button}
                  variant={"outlined"}
                  onClick={handleReset}
                >
                  <UndoIcon />
                </Button>
              </Box>
            </Box>
          </Box>
        );
      }}
    />
  );
};
