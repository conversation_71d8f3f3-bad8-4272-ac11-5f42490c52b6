import {
  Box,
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
  Typography,
} from "@mui/material";
import { FC, useMemo } from "react";
import { Controller } from "react-hook-form";
import { extendedBooleanFields } from "../../../helpers/documentTypes.ts";
import {
  StyleCreator,
  useStyleCreator,
} from "../../../hooks/useStyleCreator.ts";
import { FieldProps } from "../segment.types.ts";
import {
  addMark,
  addTextDecoration,
  getColorByRequired,
} from "./utils/fields.utils.ts";

const styles: StyleCreator<"container" | "button" | "input"> = () => ({
  container: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    gap: "24px",
  },
  input: {
    width: "100%",
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    gap: "8px",
  },
  button: {
    height: "40px",
    width: "40px",
    minWidth: "40px",
  },
});

export const SegmentBooleanField: FC<FieldProps> = ({
  base,
  name,
  label,
  level = 0,
}) => {
  const c = useStyleCreator(styles);
  const fieldKey = useMemo(() => `${base}.${name}.value`, [base, name]);

  const extended = useMemo(() => extendedBooleanFields.includes(name), [name]);

  return (
    <Controller
      name={fieldKey}
      render={({ field: { value, onChange, ref } }) => {
        if (value === "" && !extended) onChange("N");
        if (value === "" && extended) onChange(null);

        return (
          <Box sx={c.container}>
            <Typography
              textTransform={"capitalize"}
              style={{
                minWidth: "35%",
                paddingLeft: `${level * 40}px`,
                color: getColorByRequired(fieldKey),
                textDecoration: addTextDecoration(fieldKey),
              }}
              fontWeight={value ? 500 : 700}
            >
              {label}
              {addMark(fieldKey)}:
            </Typography>
            <Box sx={c.input}>
              <FormControl>
                <RadioGroup
                  row
                  name={`${base}.${name}.value`}
                  value={value}
                  onChange={onChange}
                  ref={ref}
                >
                  <FormControlLabel
                    value={"Y"}
                    control={<Radio />}
                    label="True"
                  />
                  <FormControlLabel
                    value={"N"}
                    control={<Radio />}
                    label="False"
                  />
                  {extended && (
                    <FormControlLabel
                      value={null}
                      control={<Radio />}
                      label="N/A"
                    />
                  )}
                </RadioGroup>
              </FormControl>
            </Box>
          </Box>
        );
      }}
    />
  );
};
