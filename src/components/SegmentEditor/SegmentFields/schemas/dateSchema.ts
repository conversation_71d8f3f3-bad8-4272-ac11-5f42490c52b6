import { isValid, isWithinInterval, parse } from "date-fns";
import { string } from "yup";

export const dateSchema = string()
  .trim()
  .nullable()
  .optional()
  .test({
    name: "date_field_test",
    message: "Date must be a correct string",
    test: (value) => {
      const date = parse(value, "yyyyMMdd", new Date());
      return isValid(date) && value.length === 8;
    },
  })
  .test({
    name: "date_field_test",
    message: `Year must be between 1900 and ${new Date().getFullYear()}.`,
    test: (value) => {
      const date = parse(value, "yyyyMMdd", new Date());
      const end = new Date();
      const start = new Date(1900, 0, 0);

      return isWithinInterval(date, { start, end });
    },
  });
