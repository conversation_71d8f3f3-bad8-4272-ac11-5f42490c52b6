import { string } from "yup";

export const claimnumberSchema = string()
  .trim()
  .nullable()
  .optional()
  .min(6, "Claim Number should be at least 6 characters.")
  .max(20, "Claim Number should be maximum 20 characters.")
  .test({
    name: "claimnumber_field_test",
    message: "Claim Number cannot contain special characters.",
    test: (value) => {
      if (value.includes("-001") || value.includes("-1AC")) {
        return true;
      }
      return !/[^a-zA-Z0-9 \-,.&#@]/.test(value);
    },
  });
