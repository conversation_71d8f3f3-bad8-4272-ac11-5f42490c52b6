import { claimnumberSchema } from "../schemas/claimnumberSchema.ts";
import { dateSchema } from "../schemas/dateSchema.ts";
import { defaultSchema } from "../schemas/defaultSchema.ts";
import { emailSchema } from "../schemas/emailSchema.ts";
import { numberSchema } from "../schemas/numberSchema.ts";
import { patientnameSchema } from "../schemas/patientnameSchema.ts";
import { treatmentsSchema } from "../schemas/treatmentsSchema.ts";
import { isDateField, isStringField, isZipField } from "./fields.utils.ts";

export const getValidationSchema = (key: string) => {
  switch (true) {
    case isStringField(key):
      return defaultSchema;
    case isDateField(key.toLowerCase()):
      return dateSchema;
    case key.toLowerCase().includes("claimnumber"):
      return claimnumberSchema;
    case key.toLowerCase().includes("patientname"):
      return patientnameSchema;
    case key.toLowerCase().includes("number") ||
      key.toLowerCase().includes("quantity") ||
      isZipField(key.toLowerCase()):
      return numberSchema;
    case key.toLowerCase().includes("email"):
      return emailSchema;
    case [
      "expeditedFlag",
      "rushFlag",
      "newSubmission",
      "resubmission",
      "writtenConfirmPriorOralRequest",
    ].some((item) => item.toLowerCase() === key.toLowerCase()):
      return treatmentsSchema;
    default:
      return defaultSchema;
  }
};
