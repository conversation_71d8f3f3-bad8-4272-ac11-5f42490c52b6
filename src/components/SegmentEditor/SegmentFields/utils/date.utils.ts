const isNamingDataDocDate = (key: string) =>
  key.toLowerCase().includes("namingData.docDate".toLowerCase());

const isMetaDataDocDate = (key: string) =>
  key.toLowerCase().includes("metaData.docDate".toLowerCase());

const isNamingDataDocReceivedDate = (key: string) =>
  key.toLowerCase().includes("namingData.docReceivedDate".toLowerCase());

const isMetaDataDocReceivedDate = (key: string) =>
  key.toLowerCase().includes("metaData.docReceivedDate".toLowerCase());

export const getDateFieldChangeHandler = (
  index: number,
  key: string,
  onChange: (...event: any[]) => void,
  setValue: (name: string, value: any) => void,
  value: string,
) => {
  switch (true) {
    case isNamingDataDocDate(key) || isMetaDataDocDate(key): {
      setValue(`metadata.${index}.metaData.docDate.value`, value);
      setValue(`metadata.${index}.namingData.docDate.value`, value);
      break;
    }
    case isNamingDataDocReceivedDate(key) || isMetaDataDocReceivedDate(key): {
      setValue(`metadata.${index}.metaData.docReceivedDate.value`, value);
      setValue(`metadata.${index}.namingData.docReceivedDate.value`, value);
      break;
    }
    default:
      onChange(value);
  }
};
