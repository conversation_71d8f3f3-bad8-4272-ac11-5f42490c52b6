const isNamingDataState = (key: string) =>
  key.toLowerCase().includes("namingData.docState".toLowerCase());

const isMetaDataState = (key: string) =>
  key.toLowerCase().includes("metaData.jurisdiction".toLowerCase());

export const getStateFieldChangeHandler = (
  index: number,
  key: string,
  onChange: (...event: any[]) => void,
  setValue: (name: string, value: any) => void,
  value: string,
) => {
  switch (true) {
    case isNamingDataState(key) || isMetaDataState(key): {
      setValue(`metadata.${index}.metaData.jurisdiction.value`, value);
      setValue(`metadata.${index}.namingData.docState.value`, value);
      break;
    }
    default:
      onChange(value);
  }
};
