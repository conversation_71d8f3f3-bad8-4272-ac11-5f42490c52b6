export const isConstructedField = (key: string) => {
  return ["namingdata.patientName", "physicianName"].some((item) =>
    key.toLowerCase().includes(item.toLowerCase()),
  );
};

export const isPatientNameField = (key: string) => {
  return key.toLowerCase().includes("namingdata.patientname");
};

export const isPhysicianNameField = (key: string) => {
  return key.toLowerCase().includes("physicianname");
};
