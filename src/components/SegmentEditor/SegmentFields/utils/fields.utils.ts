import {
  booleanFields,
  dateFields,
  dropdownFields,
  extendedBooleanFields,
  requiredFieldsToMark,
  requiredIfAvailable,
  stringFields,
} from "../../../../helpers/documentTypes.ts";

export const requiredColors = {
  req: "#b80000",
  ava: "#733003",
};

export const isFieldRequired = (key: string) => {
  return Boolean(
    requiredFieldsToMark.find((key_to_mark) =>
      key.toLowerCase().includes(key_to_mark.toLowerCase()),
    ),
  );
};

export const isFieldRequiredIfAvailable = (key: string) => {
  return Boolean(
    requiredIfAvailable.find((key_to_mark) =>
      key.toLowerCase().includes(key_to_mark.toLowerCase()),
    ),
  );
};

export const getFiledRequirements = (key) => {
  if (isFieldRequired(key)) return "req";
  if (isFieldRequiredIfAvailable(key)) return "ava";
  return null;
};

export const getColorByRequired = (key: string): string => {
  return requiredColors[getFiledRequirements(key)];
};

export const addMark = (key: string): string => {
  switch (getFiledRequirements(key)) {
    case "ava":
      return "'";
    case "req":
      return "*";
    default:
      return "";
  }
};

export const addTextDecoration = (key: string): string => {
  switch (getFiledRequirements(key)) {
    case "ava":
      return "none";
    case "req":
      return "underline";
    default:
      return "none";
  }
};

export const isStringField = (key: string) => {
  return stringFields.some((item) =>
    key.toLowerCase().includes(item.toLowerCase()),
  );
};

export const isBooleanField = (key: string) => {
  return [...booleanFields, ...extendedBooleanFields].some((item) =>
    key.toLowerCase().includes(item.toLowerCase()),
  );
};

export const isDropdownField = (key: string) => {
  return Object.keys(dropdownFields).some((item) =>
    key.toLowerCase().includes(item.toLowerCase()),
  );
};

export const isDateField = (key: string) => {
  return dateFields.some((item) =>
    key.toLowerCase().includes(item.toLowerCase()),
  );
};

export const isStateField = (key: string) => {
  return (
    key.toLowerCase().includes("state") ||
    key.toLowerCase().includes("jurisdiction")
  );
};

export const isPhoneField = (key: string) => {
  return (
    key.toLowerCase().includes("phonenumber") ||
    key.toLowerCase().includes("faxnumber")
  );
};

export const isZipField = (key: string) => {
  return key.toLowerCase().includes("zip");
};

export const isGenderField = (key: string) => {
  return key.toLowerCase().includes("gender");
};

export const isSnnField = (key: string) => {
  return key.toLowerCase().includes("ssn");
};

export const isQuantityField = (key: string) => {
  return key.toLowerCase().includes("quantity");
};

export const isSummaryField = (key: string) => {
  return key.toLowerCase().includes("summary");
};
