// senderName
const isNamingDataSenderName = (key: string) =>
  key.toLowerCase().includes("namingData.senderName".toLowerCase());

const isMetaDataSenderName = (key: string) =>
  key.toLowerCase().includes("metaData.senderName".toLowerCase());

// claimNumber
const isNamingDataClaimNumber = (key: string) =>
  key.toLowerCase().includes("namingData.claimNumber".toLowerCase());

const isMetaDataClaimNumber = (key: string) =>
  key.toLowerCase().includes("metaData.claim.claimNumber".toLowerCase());

export const getTextFieldChangeHandler = (
  index: number,
  key: string,
  onChange: (...event: any[]) => void,
  setValue: (name: string, value: any) => void,
  value: string,
) => {
  switch (true) {
    case isNamingDataSenderName(key) || isMetaDataSenderName(key): {
      setValue(`metadata.${index}.metaData.senderName.value`, value);
      setValue(`metadata.${index}.namingData.senderName.value`, value);
      break;
    }
    case isNamingDataClaimNumber(key) || isMetaDataClaimNumber(key): {
      setValue(`metadata.${index}.metaData.claim.claimNumber.value`, value);
      setValue(`metadata.${index}.namingData.claimNumber.value`, value);
      break;
    }
    default:
      onChange(value);
  }
};
