import UndoIcon from "@mui/icons-material/Undo";
import { Box, Button, Tooltip, Typography } from "@mui/material";
import { FC, useCallback, useMemo } from "react";
import { Controller, useFormContext } from "react-hook-form";
import { PatternFormat } from "react-number-format";
import {
  StyleCreator,
  useStyleCreator,
} from "../../../hooks/useStyleCreator.ts";
import { ExtractTextButton } from "../SegmentConstructions/ExtractTextButton.tsx";
import { SegmentFieldValidator } from "../SegmentConstructions/SegmentFieldValidator.tsx";
import { TextFieldToMask } from "../SegmentConstructions/TextFieldToMask.tsx";
import { FieldProps, PackageToUpdate } from "../segment.types.ts";
import {
  addMark,
  addTextDecoration,
  getColorByRequired,
} from "./utils/fields.utils.ts";

const styles: StyleCreator<"container" | "button" | "input"> = () => ({
  container: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    gap: "24px",
  },
  input: {
    width: "100%",
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    gap: "8px",
  },
  button: {
    height: "40px",
    width: "40px",
    minWidth: "40px",
  },
});

export const SegmentPhoneField: FC<FieldProps> = ({
  base,
  name,
  label,
  level = 0,
  validation,
}) => {
  const c = useStyleCreator(styles);
  const fieldKey = useMemo(() => `${base}.${name}.value`, [base, name]);

  const { resetField } = useFormContext<PackageToUpdate>();
  const handleReset = useCallback(
    () => resetField(fieldKey as "metadata"),
    [fieldKey, resetField],
  );

  return (
    <Controller
      name={fieldKey}
      render={({ field: { value, onChange, ref }, fieldState }) => {
        return (
          <Box sx={c.container}>
            <SegmentFieldValidator
              validation={validation}
              fieldKey={fieldKey}
              value={value}
            />
            <Typography
              textTransform={"capitalize"}
              style={{
                minWidth: "35%",
                paddingLeft: `${level * 40}px`,
                color: getColorByRequired(fieldKey),
                textDecoration: addTextDecoration(fieldKey),
              }}
              fontWeight={value ? 500 : 700}
            >
              {label}
              {addMark(fieldKey)}:
            </Typography>
            <Box sx={c.input}>
              <Tooltip title={fieldState.error?.message ?? null}>
                <PatternFormat
                  inputRef={ref}
                  value={value}
                  onValueChange={(values) => onChange(values.value)}
                  format="##########"
                  placeholder="Enter phone/fax number"
                  mask="_"
                  tabIndex={1}
                  customInput={TextFieldToMask}
                  error={fieldState.invalid}
                />
              </Tooltip>
              <ExtractTextButton onChange={onChange} fieldKey={fieldKey} />
              <Button
                tabIndex={-1}
                sx={c.button}
                variant={"outlined"}
                onClick={handleReset}
              >
                <UndoIcon />
              </Button>
            </Box>
          </Box>
        );
      }}
    />
  );
};
