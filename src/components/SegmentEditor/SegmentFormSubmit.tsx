import {
  Box,
  Button,
  Checkbox,
  FormControlLabel,
  TextField,
} from "@mui/material";
import { FC, useState } from "react";
import { Controller, useFormContext } from "react-hook-form";
import { useAuthContext } from "../../hooks/useAuth.ts";
import { useScreenshotContext } from "../../hooks/useScreenshotContext.ts";

interface Props {
  onBack: () => void;
  loading: boolean;
}

export const SegmentFormSubmit: FC<Props> = ({ onBack, loading }) => {
  const { role } = useAuthContext();
  const { setValue } = useFormContext();
  const [approved, setApproved] = useState(false);
  const { isProcessing, base64 } = useScreenshotContext();

  return (
    <Box sx={{ display: "flex", gap: "16px" }}>
      <Controller
        name={"comment"}
        render={({ field: { value, onChange, ref }, fieldState }) => {
          return (
            <TextField
              type={"text"}
              value={value === null ? "" : value}
              onChange={onChange}
              ref={ref}
              size={"small"}
              fullWidth
              error={fieldState.invalid && fieldState.isDirty}
              label={"Add comment (optional)"}
              multiline
              rows={3.5}
              disabled={role !== "user"}
            />
          );
        }}
      />
      <Box
        sx={{
          width: "100%",
          display: "flex",
          flexDirection: "column",
          justifyContent: "space-evenly",
        }}
      >
        <FormControlLabel
          value={approved}
          onChange={(_, value) => setApproved(value)}
          control={<Checkbox />}
          label="I approve that metadata has been reviewed and is correct"
        />
        <Box
          sx={{
            width: "100%",
            display: "flex",
            gap: "8px",
          }}
        >
          <Button
            size={"small"}
            fullWidth
            variant={"outlined"}
            onClick={onBack}
          >
            Back
          </Button>
          <Button
            size={"small"}
            fullWidth
            variant={"contained"}
            color={"error"}
            type={"reset"}
          >
            Reset ALl
          </Button>
          {role === "user" && (
            <Button
              size={"small"}
              fullWidth
              variant={"contained"}
              color={"warning"}
              type={"submit"}
              onClick={() => setValue("to_delegate", true)}
              disabled={!approved || loading || isProcessing || Boolean(base64)}
            >
              Delegate
            </Button>
          )}
          <Button
            size={"small"}
            fullWidth
            variant={"contained"}
            color={"success"}
            type={"submit"}
            disabled={!approved || loading || isProcessing || Boolean(base64)}
            onClick={() => setValue("to_delegate", false)}
          >
            Submit
          </Button>
        </Box>
      </Box>
    </Box>
  );
};
