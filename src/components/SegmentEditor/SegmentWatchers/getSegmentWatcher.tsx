import { FC, useCallback, useEffect, useMemo } from "react";
import { useFormContext, useWatch } from "react-hook-form";
import { PackageToUpdate } from "../segment.types.ts";

interface Props {
  field: string;
  path_to_watch: string;
  path_to_update: string[];
  index_to_skip?: number[];
}

export const getSegmentWatcher = ({
  field,
  path_to_watch,
  path_to_update,
  index_to_skip = [],
}: Props): FC<{ index: number }> => {
  return ({ index }) => {
    const { control, setValue, formState, getValues } =
      useFormContext<PackageToUpdate>();
    const metadata_length = useMemo(
      () => formState.defaultValues.metadata.length,
      [formState.defaultValues.metadata.length],
    );

    const value = useWatch({
      control,
      // @ts-expect-error eslint-disable-line @typescript-eslint/ban-ts-comment
      name: `metadata.${index}.${path_to_watch}.${field}.value`,
    });

    const populate = useCallback(
      async ({ value }) => {
        for (let i = 0; i < metadata_length; i++) {
          if ([index, ...index_to_skip].includes(i)) continue;

          path_to_update.map((path) => {
            // @ts-expect-error eslint-disable-line @typescript-eslint/ban-ts-comment
            const ex_value = getValues(`metadata.${i}.${path}.${field}.value`);
            if (ex_value !== undefined) {
              // @ts-expect-error eslint-disable-line @typescript-eslint/ban-ts-comment
              setValue(`metadata.${i}.${path}.${field}.value`, value);
            }
          });
        }
      },
      [getValues, index, metadata_length, setValue],
    );

    useEffect(() => {
      if ((value as unknown as string) !== "") populate({ value });
    }, [value, setValue, index, populate]);

    return null;
  };
};
