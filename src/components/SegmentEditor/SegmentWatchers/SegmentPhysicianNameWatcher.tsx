import { FC, useEffect } from "react";
import { useFormContext, useWatch } from "react-hook-form";
import { PackageToUpdate } from "../segment.types.ts";

interface Props {
  index: number;
}

export const SegmentPhysicianNameWatcher: FC<Props> = ({ index }) => {
  const { control, setValue } = useFormContext<PackageToUpdate>();

  const physicianFirstName = useWatch({
    control,
    name: `metadata.${index}.metaData.physician.firstName.value`,
  });

  const physicianLastName = useWatch({
    control,
    name: `metadata.${index}.metaData.physician.lastName.value`,
  });

  useEffect(() => {
    const physicianNameList = [physicianFirstName, physicianLastName].filter(
      Boolean,
    );

    setValue(
      `metadata.${index}.metaData.physician.physicianName.value`,
      physicianNameList.join(" "),
    );
  }, [setValue, index, physicianFirstName, physicianLastName]);

  return null;
};
