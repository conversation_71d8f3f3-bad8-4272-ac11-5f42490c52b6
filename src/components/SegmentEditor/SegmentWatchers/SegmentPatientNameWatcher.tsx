import { FC, useEffect } from "react";
import { useFormContext, useWatch } from "react-hook-form";
import { PackageToUpdate } from "../segment.types.ts";

interface Props {
  index: number;
}

export const SegmentPatientNameWatcher: FC<Props> = ({ index }) => {
  const { control, setValue } = useFormContext<PackageToUpdate>();

  const patientFirstName = useWatch({
    control,
    name: `metadata.${index}.namingData.patientFirstName.value`,
  });

  const patientMiddleName = useWatch({
    control,
    name: `metadata.${index}.namingData.patientMiddleName.value`,
  });

  const patientLastName = useWatch({
    control,
    name: `metadata.${index}.namingData.patientLastName.value`,
  });

  useEffect(() => {
    const patientNameList = [
      patientFirstName,
      patientMiddleName,
      patientLastName,
    ].filter(Boolean);

    setValue(
      `metadata.${index}.namingData.patientName.value`,
      patientNameList.join(" "),
      { shouldTouch: true },
    );
  }, [patientFirstName, patientMiddleName, patientLastName, setValue, index]);

  return null;
};
