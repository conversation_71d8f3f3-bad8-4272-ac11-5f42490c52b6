import { Box, Button, ButtonGroup } from "@mui/material";
import { FC, useCallback, useMemo, useState } from "react";
import Scrollbar from "react-scrollbars-custom";
import { StyleCreator, useStyleCreator } from "../../hooks/useStyleCreator.ts";
import { SegmentObjectField } from "./SegmentConstructions/SegmentObjectField.tsx";
import { MetadataToUpdate } from "./segment.types.ts";

interface Props {
  index: number;
  metadata: MetadataToUpdate;
  fieldsToValidate: string[];
}

const styles: StyleCreator<"container"> = () => ({
  container: {
    height: "100%",
    border: "1px 1px solid #FAEED1",
    borderRadius: "8px",
    display: "flex",
    flexDirection: "column",
    gap: "8px",
  },
});

type DataType = "namingData" | "metaData";

export const SegmentSubform: FC<Props> = ({
  index,
  metadata,
  fieldsToValidate,
}) => {
  const c = useStyleCreator(styles);

  const [activeData, setActiveData] = useState<DataType>(
    fieldsToValidate[0] as DataType,
  );
  const isButtonActive = useCallback(
    (type: DataType) => (activeData === type ? "contained" : "outlined"),
    [activeData],
  );

  const keys = useMemo(
    () => Object.keys(metadata[activeData]),
    [activeData, metadata],
  );

  return (
    <Box sx={c.container}>
      <ButtonGroup fullWidth variant="outlined">
        {fieldsToValidate
          .map((value) => value.toLowerCase())
          .includes("namingdata") && (
          <Button
            variant={isButtonActive("namingData")}
            onClick={() => setActiveData("namingData")}
          >
            Naming Data
          </Button>
        )}
        {fieldsToValidate
          .map((value) => value.toLowerCase())
          .includes("metadata") && (
          <Button
            variant={isButtonActive(`metaData`)}
            onClick={() => setActiveData(`metaData`)}
          >
            Metadata
          </Button>
        )}
      </ButtonGroup>
      <Scrollbar
        contentProps={{
          style: {
            padding: "0 8px",
            display: "flex",
            flexDirection: "column",
            gap: "8px",
          },
        }}
      >
        {/*<SegmentWatchers index={index} />*/}
        {keys.map((key) => (
          <SegmentObjectField
            key={key}
            index={index}
            activeData={activeData}
            metadata={metadata[activeData]}
            base={`metadata.${index}.${activeData}`}
            name={key}
            label={key}
            level={0}
          />
        ))}
      </Scrollbar>
    </Box>
  );
};
