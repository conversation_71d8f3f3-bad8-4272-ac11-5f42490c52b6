import { Chevron<PERSON>eft, ChevronRight, Menu } from "@mui/icons-material";
import { Box, Button, Typography } from "@mui/material";
import { Dispatch, FC, SetStateAction, useCallback, useMemo } from "react";
import { useFormContext, useWatch } from "react-hook-form";
import { StyleCreator, useStyleCreator } from "../../hooks/useStyleCreator.ts";
import { SegmentUpdateSubscriber } from "./SegmentConstructions/SegmentUpdateSubscriber.tsx";
import { SegmentSubform } from "./SegmentSubform.tsx";
import { PackageToUpdate } from "./segment.types.ts";

interface Props {
  sliceToShow: number;
  setOpen: Dispatch<SetStateAction<boolean>>;
  setSliceToShow: Dispatch<SetStateAction<number>>;
  fieldsToValidate: string[];
}

const styles: StyleCreator<"container" | "navigation"> = () => ({
  container: {
    height: "100%",
    display: "flex",
    flexDirection: "column",
    gap: "16px",
    position: "relative",
  },
  navigation: {
    display: "flex",
    alignItems: "center",
    width: "100%",
    justifyContent: "center",
    gap: "12px",
  },
});

const getNextIndex = (
  slices: EntryListItem[],
  currentIndex: number,
  direction: "prev" | "next",
) => {
  switch (direction) {
    case "next": {
      return slices.findIndex(({ incomplete, duplicate_of }, index) => {
        return index > currentIndex && !incomplete && !duplicate_of;
      });
    }
    case "prev": {
      for (let index = slices.length; index >= 0; index--) {
        const slice = slices[index];
        if (index < currentIndex && !slice.duplicate_of && !slice.incomplete)
          return index;
      }
    }
  }
};

export const SegmentForm: FC<Props> = ({
  setOpen,
  sliceToShow,
  setSliceToShow,
  fieldsToValidate,
}) => {
  const c = useStyleCreator(styles);

  const { control } = useFormContext<PackageToUpdate>();
  const metadata = useWatch({ control, name: "metadata" });
  const slices = useWatch({ control, name: "predictions" });

  const selectedMetadata = useMemo(
    () => metadata[sliceToShow],
    [metadata, sliceToShow],
  );

  const selectedSlice = useMemo(
    () => slices[sliceToShow],
    [slices, sliceToShow],
  );

  const navigate = useCallback(
    (direction: "prev" | "next") => () =>
      setSliceToShow((prev) => {
        const index = getNextIndex(slices, prev, direction);
        if (index === undefined || index === -1) return prev;
        return index;
      }),
    [setSliceToShow, slices],
  );

  const slicePageRanges = useCallback((ranges: [number, number][]) => {
    return ranges.map((range) => range.join("-")).join(" | ");
  }, []);

  return (
    <Box sx={c.container}>
      <SegmentUpdateSubscriber />
      <Box sx={{ display: "flex", gap: "12px", alignItems: "center" }}>
        <Button
          sx={{ height: "100%" }}
          variant={"outlined"}
          onClick={() => setOpen(true)}
        >
          <Menu />
        </Button>
        <Box>
          <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <Typography typography={"h5"}>
              Document type: {selectedMetadata.docType}
            </Typography>
            {selectedSlice.incomplete && (
              <Typography typography={"h7"} color={"yellow"}>
                (incomplete)
              </Typography>
            )}
            {selectedSlice.duplicate_of && (
              <Typography typography={"h7"} color={"yellow"}>
                (duplicated)
              </Typography>
            )}
            {selectedSlice.other_patient && (
              <Typography typography={"h7"} color={"yellow"}>
                (other patient)
              </Typography>
            )}
          </Box>
          <Typography typography={"h6"}>
            Pages: {slicePageRanges(selectedMetadata.pagesRef)}
          </Typography>
        </Box>
      </Box>
      <SegmentSubform
        index={sliceToShow}
        metadata={selectedMetadata}
        fieldsToValidate={fieldsToValidate}
      />
      <Box sx={c.navigation}>
        <Button variant={"outlined"} onClick={navigate("prev")}>
          <ChevronLeft />
        </Button>
        <Button variant={"outlined"} onClick={navigate("next")}>
          <ChevronRight />
        </Button>
      </Box>
    </Box>
  );
};
