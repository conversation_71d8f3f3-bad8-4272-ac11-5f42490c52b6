import { Checkbox, FormControlLabel } from "@mui/material";
import { Controller } from "react-hook-form";

export const SliceEditDialogOtherPatientMark = () => {
  return (
    <Controller
      name={"other_patient"}
      render={({ field: { value, onChange, ref } }) => (
        <FormControlLabel
          control={<Checkbox checked={value} onChange={onChange} ref={ref} />}
          label="Other patient"
        />
      )}
    />
  );
};
