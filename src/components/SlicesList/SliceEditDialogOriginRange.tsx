import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Divider,
  Typography,
} from "@mui/material";
import Menu from "@mui/material/Menu";
import * as _ from "lodash";
import { FC, useCallback, useEffect, useState } from "react";
import { Controller, useFormContext, useWatch } from "react-hook-form";
import Scrollbar from "react-scrollbars-custom";
import { StyleCreator, useStyleCreator } from "../../hooks/useStyleCreator.ts";

const styles: StyleCreator<any> = () => ({
  row: {
    display: "flex",
    gap: "8px",
    alignItems: "center",
  },
  button: {
    minWidth: "36px",
    width: "36px",
  },
});

export const SliceEditDialogOriginRange: FC<{
  slices: EntryListItem[];
}> = ({ slices }) => {
  const c = useStyleCreator(styles);

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClose = () => {
    setAnchorEl(null);
  };

  const { control, setValue } = useFormContext();
  const is_duplicated = useWatch({ control, name: "is_duplicated" });
  const origin = useWatch({ control, name: "origin" });

  const slicePageRanges = useCallback((ranges: number[][]) => {
    return ranges.map((range) => range.join("-")).join(" | ");
  }, []);

  useEffect(() => {
    if (!is_duplicated && origin) {
      setValue("origin", null);
    }
  }, [is_duplicated, origin, setValue]);

  return (
    <>
      <Collapse in={is_duplicated}>
        <DialogContent sx={{ padding: "12px 20px" }}>
          <Box sx={c.row}>
            <Typography typography={"body1"} fontWeight={700}>
              Select origin:
            </Typography>
            <Controller
              name={`origin`}
              render={({ field: { value, onChange } }) => {
                return (
                  <>
                    <Button
                      variant={"outlined"}
                      onClick={(event) => setAnchorEl(event.currentTarget)}
                      fullWidth
                      sx={{ height: "60px", justifyContent: "start" }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "start",
                          justifyContent: "start",
                          flexDirection: "column",
                          gap: "2px",
                        }}
                      >
                        {!value && "Slice"}
                        {value && (
                          <>
                            <Typography>{value.packet_type}</Typography>
                            <Typography typography={"caption"} color={"white"}>
                              Pages: {slicePageRanges(value.page_ranges)}
                            </Typography>
                          </>
                        )}
                      </Box>
                    </Button>
                    <Menu
                      sx={{
                        "& .MuiPaper-root": {
                          marginTop: "8px",
                          width: "320px",
                          height: `${slices.length * 60 + 8 * slices.length}px`,
                          maxHeight: "250px",
                          padding: "0 4px",
                        },
                        "& .MuiList-root": {
                          height: `${slices.length * 60 + 8 * slices.length}px`,
                          maxHeight: "250px",
                        },
                      }}
                      anchorOrigin={{
                        vertical: "bottom",
                        horizontal: "center",
                      }}
                      transformOrigin={{
                        vertical: "top",
                        horizontal: "center",
                      }}
                      anchorEl={anchorEl}
                      open={open}
                      onClose={handleClose}
                    >
                      <Scrollbar
                        contentProps={{
                          style: {
                            display: "flex",
                            flexDirection: "column",
                            gap: "4px",
                          },
                        }}
                      >
                        {slices.map(({ packet_type, page_ranges }, index) => (
                          <Button
                            key={`${slicePageRanges(page_ranges)}-${index}`}
                            sx={{ justifyContent: "start" }}
                            onClick={() => {
                              onChange({
                                packet_type,
                                page_ranges,
                              });
                              handleClose();
                            }}
                            disabled={_.isEqual(value, {
                              packet_type,
                              page_ranges,
                            })}
                          >
                            <Box
                              sx={{
                                display: "flex",
                                alignItems: "start",
                                justifyContent: "start",
                                flexDirection: "column",
                                gap: "2px",
                              }}
                            >
                              <Typography>{packet_type}</Typography>
                              <Typography
                                typography={"caption"}
                                color={"white"}
                              >
                                Pages: {slicePageRanges(page_ranges)}
                              </Typography>
                            </Box>
                          </Button>
                        ))}
                      </Scrollbar>
                    </Menu>
                  </>
                );
              }}
            />
          </Box>
        </DialogContent>
      </Collapse>
      {is_duplicated && <Divider />}
    </>
  );
};
