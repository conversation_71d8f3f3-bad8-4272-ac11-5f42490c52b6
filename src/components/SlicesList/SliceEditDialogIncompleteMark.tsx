import { Checkbox, FormControlLabel } from "@mui/material";
import { Controller } from "react-hook-form";

export const SliceEditDialogIncompleteMark = () => {
  return (
    <Controller
      name={"is_incomplete"}
      render={({ field: { value, onChange, ref } }) => (
        <FormControlLabel
          control={<Checkbox checked={value} onChange={onChange} ref={ref} />}
          label="Incomplete"
        />
      )}
    />
  );
};
