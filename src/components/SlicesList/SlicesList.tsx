import { Box, CircularProgress } from "@mui/material";
import { CSSProperties, FC, useMemo } from "react";
import Scrollbar from "react-scrollbars-custom";
import { StyleCreator, useStyleCreator } from "../../hooks/useStyleCreator.ts";
import { useAppSelector } from "../../store/store.ts";
import { NewSliceForm } from "./NewSliceForm.tsx";
import { SliceItem } from "./SliceItem.tsx";

interface Props {
  pages: number;
  loading: boolean;
}

const styles: StyleCreator<any> = (theme) => ({
  container: {
    display: "flex",
    flexDirection: "column",
    border: "1px solid white",
    borderRadius: "8px",
    [theme.breakpoints.up(1439)]: {
      width: "100%",
    },
  },
  scrollContainer: {
    padding: "8px 16px 8px 8px",
    display: "flex",
    flexDirection: "column",
    gap: "12px",
    minHeight: "400px",
    maxHeight: "400px",
  },
});

export const SlicesList: FC<Props> = ({ pages, loading }) => {
  const c = useStyleCreator(styles);

  const { slices } = useAppSelector((store) => store.data);

  const preparedSlices = useMemo(() => {
    return [...slices].sort((slice_a, slice_b) => {
      if (!slice_a.page_ranges?.[0]?.[0] || !slice_b.page_ranges?.[0]?.[0])
        return 0;

      return slice_a.page_ranges[0][0] - slice_b.page_ranges[0][0];
    });
  }, [slices]);

  return (
    <Box sx={c.container}>
      <Scrollbar
        style={{
          minHeight: "400px",
          maxHeight: "400px",
        }}
        contentProps={{ style: c.scrollContainer as CSSProperties }}
        trackYProps={{
          style: {
            right: "8px",
          },
        }}
      >
        {!loading &&
          preparedSlices.map((slice, index) => {
            const other = slices.filter(
              ({ packet_prediction_id }) =>
                packet_prediction_id !== slice.packet_prediction_id,
            );

            return (
              <SliceItem
                key={`${slice.packet_id}-${index}`}
                slice={slice}
                slices={other}
                pages={pages}
                index={index}
              />
            );
          })}
        {loading && (
          <CircularProgress
            sx={{ alignSelf: "center", marginTop: "calc(50% - 25px)" }}
          />
        )}
      </Scrollbar>
      <NewSliceForm slices={slices} pages={pages} />
    </Box>
  );
};
