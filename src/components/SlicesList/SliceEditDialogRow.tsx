import DeleteIcon from "@mui/icons-material/Delete";
import { Box, Button, TextField } from "@mui/material";
import { FC, useCallback } from "react";
import { Controller, UseFieldArrayRemove } from "react-hook-form";
import { StyleCreator, useStyleCreator } from "../../hooks/useStyleCreator.ts";

const styles: StyleCreator<any> = (theme) => ({
  row: {
    display: "flex",
    gap: "8px",
  },
  button: {
    minWidth: "36px",
    width: "36px",
  },
});

interface Props {
  index: number;
  onRemove: UseFieldArrayRemove;
}

export const SliceEditDialogRow: FC<Props> = ({ index, onRemove }) => {
  const c = useStyleCreator(styles);

  const handleRemove = useCallback(() => onRemove(index), [index, onRemove]);

  return (
    <Box sx={c.row}>
      <Controller
        name={`ranges.${index}.start`}
        render={({ field: { value, onChange, ref } }) => (
          <TextField
            type={"number"}
            size={"small"}
            label={"Start Page"}
            value={value}
            onChange={onChange}
            ref={ref}
          />
        )}
      />
      <Controller
        name={`ranges.${index}.end`}
        render={({ field: { value, onChange, ref } }) => (
          <TextField
            type={"number"}
            size={"small"}
            label={"End Page"}
            value={value}
            onChange={onChange}
            ref={ref}
          />
        )}
      />
      <Button sx={c.button} onClick={handleRemove}>
        <DeleteIcon />
      </Button>
    </Box>
  );
};
