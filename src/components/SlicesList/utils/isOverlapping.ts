export const isOverlapping = (range1: [number, number], range2: number[]) => {
  const [start1, end1] = range1;
  const [start2, end2] = range2;

  const isStartBetween = start2 <= start1 && start1 <= end2;
  const isEndBetween = start2 <= end1 && end1 <= end2;
  const isContains =
    (start1 <= start2 && end2 <= end1) || (start2 <= start1 && end1 <= end2);

  return isStartBetween || isEndBetween || isContains;
};
