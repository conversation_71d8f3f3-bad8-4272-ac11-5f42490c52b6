import * as yup from "yup";
import { AvailableDocTypes } from "../../../helpers/documentTypes.ts";
import { isOverlapping } from "./isOverlapping.ts";

interface Props {
  pages: number;
  slices: EntryListItem[];
}

export const getNewtSliceValidationSchema = ({ pages, slices }: Props) => {
  return yup
    .object()
    .shape({
      start: yup.number().positive("Value should be positive"),
      end: yup.number().positive("Value should be positive"),
      type: yup
        .string()
        .required()
        .oneOf([...AvailableDocTypes, "Undefined"]),
    })
    .test(
      "Range error",
      `End of range should be equal to ${pages} or lower`,
      ({ end }) => {
        return end <= pages;
      },
    )
    .test("Range error", "Ranges can't overlap", ({ start, end }) => {
      return slices.every(({ page_ranges }) => {
        return page_ranges.every((range) => {
          return !isOverlapping([start, end], range);
        });
      });
    })
    .test(
      "Marking error",
      "Only edge slices can be marked as Incomplete",
      () => {
        return slices.every(({ incomplete }, index, array) => {
          if (index === 0 || index === array.length - 1) return true;
          return !incomplete;
        });
      },
    );
};
