import * as yup from "yup";
import { object } from "yup";
import { isOverlapping } from "./isOverlapping.ts";

interface Props {
  range: [number, number];
  slices: EntryListItem[];
}

export const getSlicesValidationSchema = ({ range, slices }: Props) => {
  return yup.object().shape({
    ranges: yup
      .array()
      .min(1)
      .of(
        yup.object().shape({
          start: yup.number().positive("Value should be positive"),
          end: yup.number().positive("Value should be positive"),
        }),
      )
      .test("Range error", `Start must be lower than end or equal`, (value) => {
        return value.every(({ start, end }) => start <= end);
      })
      .test(
        "Range error",
        `Start of range should be equal to ${range[0]} or higher`,
        (value) => {
          return value.every(({ start }) => start >= range[0]);
        },
      )
      .test(
        "Range error",
        `End of range should be equal to ${range[1]} or lower`,
        (value) => {
          return value.every(({ end }) => end <= range[1]);
        },
      )
      .test("Range error", `Ranges must no overlap`, (value) => {
        return value.every(({ start, end }) => {
          return slices.every(({ page_ranges }) => {
            return page_ranges.every((range) => {
              return !isOverlapping([start, end], range);
            });
          });
        });
      })
      .test("Range error", `Ranges must no overlap`, (value) => {
        if (value.length < 2) return true;
        return value.every(({ start, end }, index, array) => {
          if ([start, end].some((value) => value === null)) return false;
          return array.every((range, sub_index) => {
            if (sub_index === index) return true;
            return !isOverlapping([start, end], [range.start, range.end]);
          });
        });
      }),
    is_duplicated: yup.boolean(),
    is_incomplete: yup.boolean(),
    other_patient: yup.boolean(),
    origin: object().when("is_duplicated", {
      is: true,
      then: (schema) => schema.required(),
      otherwise: (schema) => schema.nullable(),
    }),
  });
};
