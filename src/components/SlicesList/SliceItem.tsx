import { Edit } from "@mui/icons-material";
import DeleteIcon from "@mui/icons-material/Delete";
import { Box, Button, Paper, Typography } from "@mui/material";
import { FC, useCallback, useMemo, useState } from "react";
import { StyleCreator, useStyleCreator } from "../../hooks/useStyleCreator.ts";
import { deleteSlice } from "../../store/slices/metadata/metadata.slice.ts";
import { useAppDispatch } from "../../store/store.ts";
import { SliceEditDialog } from "./SliceEditDialog.tsx";

const styles: StyleCreator<any> = (theme) => ({
  paper: {
    minWidth: "300px",
    padding: "8px",
    backgroundColor: "#333",
    flex: "0 0 auto",
    position: "relative",
  },
  container: {
    border: "1px solid #fff",
    borderRadius: "4px",
    padding: "4px 8px",
    display: "flex",
    gap: "8px",
    justifyContent: "space-between",
  },
  button: {
    width: "36px",
    maxWidth: "36px",
    minWidth: "auto",
  },
  column: {
    display: "flex",
    flexDirection: "column",
    justifyContent: "space-evenly",
  },
});

interface Props {
  slice: EntryListItem;
  slices: EntryListItem[];
  pages: number;
  index: number;
}

export const SliceItem: FC<Props> = ({ slice, slices, pages, index }) => {
  const c = useStyleCreator(styles);
  const dispatch = useAppDispatch();

  const [isDialogOpened, setIsDialogOpened] = useState(false);

  const slicePageRanges = useMemo(() => {
    return slice.page_ranges.map((range) => range.join("-")).join(" | ");
  }, [slice]);

  const normalizeConfidence = useCallback((value: number) => {
    if (value === 1) return "1.0";
    return value.toFixed(4);
  }, []);

  const handleDelete = useCallback(() => {
    dispatch(deleteSlice(slice.packet_prediction_id));
  }, [dispatch, slice]);

  const openDialog = useCallback(() => setIsDialogOpened(true), []);
  const closeDialog = useCallback(() => setIsDialogOpened(false), []);

  return (
    <Paper sx={c.paper}>
      <Box sx={c.container}>
        <Box>
          <Typography typography={"caption"}>
            <b>Document type:</b> {slice.packet_type}
          </Typography>
          <Typography typography={"caption"}>
            <b>Pages:</b> {slicePageRanges}
          </Typography>
          {slice.packet_type !== "Undefined" && (
            <>
              <Typography typography={"caption"}>
                <b>Overall Confidence:</b>{" "}
                {normalizeConfidence(slice.overall_confidence)}
              </Typography>
              <Typography typography={"caption"}>
                <b>Classification Confidence:</b>{" "}
                {normalizeConfidence(slice.classification_confidence)}
              </Typography>
            </>
          )}
          {slice.duplicate_of && (
            <Typography typography={"caption"} color={"yellow"}>
              <b>Duplicated</b>
            </Typography>
          )}
          {slice.incomplete && (
            <Typography typography={"caption"} color={"yellow"}>
              <b>Incomplete</b>
            </Typography>
          )}
          {slice.other_patient && (
            <Typography typography={"caption"} color={"yellow"}>
              <b>Other patient</b>
            </Typography>
          )}
        </Box>
        <Box
          sx={c.column}
          style={{
            flexDirection: slice.packet_type === "Undefined" ? "row" : "column",
          }}
        >
          <Button sx={c.button} onClick={openDialog}>
            <Edit />
          </Button>
          <Button sx={c.button} onClick={handleDelete}>
            <DeleteIcon />
          </Button>
        </Box>
      </Box>
      {isDialogOpened && (
        <SliceEditDialog
          slice={slice}
          slices={slices}
          pages={pages}
          pageRanges={slicePageRanges}
          open={isDialogOpened}
          onClose={closeDialog}
          isIncompleteAvailable={index === 0 || index === slices.length}
        />
      )}
    </Paper>
  );
};
