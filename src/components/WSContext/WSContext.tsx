import { useIdle, useNetworkState } from "@uidotdev/usehooks";
import axios from "axios";
import { format } from "date-fns";
import {
  FC,
  ReactNode,
  createContext,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import { getApiUrl } from "../../api/getApiUrl.ts";
import { useAuthContext } from "../../hooks/useAuth.ts";
import { WSStatusModal } from "./WSStatusModal.tsx";
import {
  AppWS,
  MessagePayload,
  OpenWSModal,
  WSClose,
  WSCloseReason,
  WSCloseStatus,
  WSWorkStatus,
} from "./types.ts";
import { refreshToken, waitForTokenRefresh } from "../../api/tokenRefresh.ts";

export const WSContext = createContext<AppWS>(null);

const log = (message: string) => {
  console.debug(`${format(new Date(), "HH:mm:ss.SSS")} | ${message}`);
};

export const AppWSProvider: FC<{ children: ReactNode }> = ({ children }) => {
  const isIdle = useIdle(300_000);
  const { online: isOnline } = useNetworkState();
  const { token } = useAuthContext();

  const [isOpen, setIsOpen] = useState(false);
  const [isReconnecting, setIsReconnecting] = useState(false);

  const [workStatus, setWorkStatus] = useState<WSWorkStatus | null>(
    WSWorkStatus.INITIALIZING,
  );
  const [closeStatus, setCloseStatus] = useState<WSCloseStatus | null>(null);

  const [connectionAttempts, setConnectionAttempts] = useState(6);

  const WS = useRef<WebSocket>(null);
  const KeepAliveInterval = useRef(null);
  const ReconnectInterval = useRef(null);
  const LostConnectionTimeout = useRef(null);
  const KeepAliveWorking = useRef(false);

  const sendMessage = useCallback((message: MessagePayload) => {
    if (WS.current && WS.current.readyState === WebSocket.OPEN) {
      WS.current.send(JSON.stringify(message));
    }
  }, []);

  const endKeepAlive = useCallback(({ reason }: { reason: string }) => {
    if (KeepAliveInterval.current) {
      window.clearInterval(KeepAliveInterval.current);
      KeepAliveInterval.current = null;
      KeepAliveWorking.current = false;
      log(`Stop KeepAlive. Reason: ${reason}`);
    }
  }, []);

  const refreshTokenIfActive = useCallback(async () => {
    try {
      // Get fresh token from localStorage every time
      const userAuth = JSON.parse(localStorage.getItem("userAuth") || "{}");
      const currentToken = userAuth.token;
      
      if (!currentToken) {
        console.error("No token found in localStorage");
        return false;
      }
      
      const response = await axios.post(
        getApiUrl({ url: "/auth/refresh-if-active" }),
        {},
        {
          headers: {
            Authorization: `Bearer ${currentToken}`, // Always use fresh token
            "x-api-key": import.meta.env.VITE_API_KEY,
          },
        }
      );
      
      if (response.data.access_token) {
        const newUserAuth = {
          ...userAuth,
          token: response.data.access_token,
          refresh: response.data.refresh_token,
          date: new Date().getTime(),
        };
        localStorage.setItem("userAuth", JSON.stringify(newUserAuth));
        log("Token refreshed based on activity");
        
        // Dispatch events to notify other components
        window.dispatchEvent(new Event("storage"));
        window.dispatchEvent(new CustomEvent("authTokenUpdated", { 
          detail: { token: response.data.access_token } 
        }));
        
        return true;
      }
    } catch (error) {
      console.error("Activity-based token refresh failed:", error);
      return false;
    }
  }, []); // No dependencies needed

  const startKeepAlive = useCallback(() => {
    endKeepAlive({ reason: "restart" });
    sendMessage({ alive: true, active: !isIdle });
    KeepAliveWorking.current = true;
    KeepAliveInterval.current = window.setInterval(
      () => sendMessage({ alive: true, active: !isIdle }),
      30000,
    );
    log("Start KeepAlive");
  }, [endKeepAlive, isIdle, sendMessage]);

  const closeWS = useCallback<WSClose>(
    ({ reason = "internal", logout = false, skip_message = false }) => {
      setIsOpen(false);
      endKeepAlive({ reason: `Closing WS. ${reason}` });
      if (WS.current && WS.current.readyState === WebSocket.OPEN) {
        if (!skip_message) {
          sendMessage({ logout });
          WS.current.close(1000, reason);
        }

        WS.current = null;
      }
    },
    [endKeepAlive, sendMessage],
  );

  const openWSModal = useCallback<OpenWSModal>(
    ({ workStatus, closeStatus }) => {
      setWorkStatus(workStatus);
      setCloseStatus(closeStatus);
    },
    [],
  );

  const WSOnError = useCallback(() => {
    log("WS Error occurred");
    endKeepAlive({ reason: "WS Error occurred" });
    setWorkStatus(WSWorkStatus.ERROR);
    setCloseStatus(WSCloseStatus.InternalError);
    closeWS({ reason: "Unexpected Error" });
  }, [closeWS, endKeepAlive]);

  let openWSRef: () => void;

const WSReconnect = useCallback(() => {
  if (isOnline) {
    log("WS Start reconnecting");
    setIsReconnecting(true);
    setWorkStatus(WSWorkStatus.RECONNECTING);
    
    if (ReconnectInterval.current) {
      window.clearInterval(ReconnectInterval.current);
    }
    
    setConnectionAttempts(12);
    
    let attemptCount = 0;
    const maxDelay = 30000;
    
    const attemptReconnect = async () => {
      attemptCount++;
      const delay = Math.min(2000 * Math.pow(1.5, attemptCount), maxDelay);
      
      log(`Reconnecting. Attempt: ${attemptCount} with delay: ${Math.round(delay/1000)}s`);
      
      try {
        // Wait for any ongoing token refresh before reconnecting
        await waitForTokenRefresh();
        log("Token refresh check complete, proceeding with reconnection");
        
        if (attemptCount % 3 === 0) {
          await refreshToken();
        }
        // Use the reference instead of direct function call
        openWSRef();
      } catch (err) {
        console.error("Reconnection attempt failed:", err);
      }
    };
    
    attemptReconnect();
    
    ReconnectInterval.current = window.setInterval(() => {
      setConnectionAttempts(prev => {
        if (prev <= 1) {
          window.clearInterval(ReconnectInterval.current);
          setIsReconnecting(false);
          setCloseStatus(WSCloseStatus.ReconnectError);
          setWorkStatus(WSWorkStatus.ERROR);
          return 0;
        }
        attemptReconnect();
        return prev - 1;
      });
    }, 5000);
  }
}, [isOnline]); // Remove openWS from dependencies

  const WSOnClose = useCallback(
    (event: CloseEvent) => {
      setWorkStatus(WSWorkStatus.ERROR);
      const reason = event.reason as WSCloseReason;
      const preparedReason = reason.length > 0 ? reason : "Unexpected error";

      log(`WS Closed due to ${preparedReason}. Code: ${event.code}`);
      endKeepAlive({ reason: `Closing WS. ${reason}` });
      switch (reason) {
        case "Authorization is not provided":
        case "Could not validate token / Token expired":
        case "401: Could not validate token":
        case "Invalid Token": {
          // Try to refresh the token immediately on auth errors
          refreshToken().then(newToken => {
            if (newToken) {
              // If we got a new token, try reconnecting right away
              setTimeout(() => openWS(), 1000);
            } else {
              // If token refresh failed, show auth error
              setCloseStatus(WSCloseStatus.AuthError);
              closeWS({ reason: "Authorization error" });
            }
          });
          break;
        }
        case "TimeoutError":
        case "timeout_error": {
          setCloseStatus(WSCloseStatus.TimeoutError);
          closeWS({ reason: "Timeout" });
          break;
        }
        case "Same user different connection": {
          setCloseStatus(WSCloseStatus.SameUserError);
          closeWS({ reason: "Other session opened" });
          break;
        }
        case "Same package different connection": {
          setCloseStatus(WSCloseStatus.SamePackageError);
          closeWS({ reason: "Same package different connection" });
          break;
        }
        case "internal_error": {
          setCloseStatus(WSCloseStatus.InternalError);
          closeWS({ reason: "internal_error" });
          break;
        }
        case "internal":
        case "Logout": {
          setCloseStatus(null);
          setWorkStatus(null);
          closeWS({ reason: "Logout" });
          break;
        }
        case "401: Token expired": {
          WSReconnect();
          break;
        }
        default: {
          if (![1000, 1001].includes(event.code)) WSReconnect();
          break;
        }
      }
    },
    [WSReconnect, closeWS, endKeepAlive],
  );

  const openWS = useCallback(() => {
    log("WS Opening");

    // Get fresh token from localStorage
    const userAuth = JSON.parse(localStorage.getItem("userAuth") || "{}");
    const currentToken = userAuth.token;
    
    if (!currentToken) {
      log("No token available for WebSocket connection");
      setWorkStatus(WSWorkStatus.ERROR);
      setCloseStatus(WSCloseStatus.AuthError);
      return;
    }

    closeWS({ reason: "Cleanup" });
    axios
      .get(getApiUrl({ url: "/ws/token" }), {
        headers: {
          Authorization: `Bearer ${currentToken}`, // Use fresh token
          "x-api-key": import.meta.env.VITE_API_KEY,
        },
      })
      .then(({ data }) => {
        const WSLink = `${import.meta.env.VITE_WS_URL}${data.ws_token}`;
        WS.current = new WebSocket(WSLink);

        WS.current.onopen = () => {
          log("WS Opened");
          setWorkStatus(null);
          setCloseStatus(null);
          setIsOpen(true);
          setIsReconnecting(false);
          setConnectionAttempts(6);
          setWorkStatus(WSWorkStatus.OK);
          window.clearInterval(ReconnectInterval.current);
        };
        WS.current.onerror = WSOnError;
        WS.current.onclose = WSOnClose;
      })
      .catch((error) => {
        console.error("Failed to get WebSocket token:", error);
        WSOnError();
      });
  }, [WSOnClose, WSOnError, closeWS]); // Remove token dependency
  openWSRef = openWS;
  useEffect(() => {
    if (isOnline && isIdle && WS.current) {
      // Don't completely stop keepalives, just reduce their frequency
      if (KeepAliveInterval.current) {
        window.clearInterval(KeepAliveInterval.current);
        
        // Set up a less frequent keepalive to maintain session
        KeepAliveInterval.current = window.setInterval(
          () => sendMessage({ alive: true, idle: true }),
          120000 // Every 2 minutes instead of every 30 seconds
        );
      }
      setWorkStatus(WSWorkStatus.AFK);
    } else if (isOnline && !isIdle && workStatus === WSWorkStatus.AFK) {
      // User returned from AFK - resume normal keepalive frequency
      if (WS.current && WS.current.readyState === WebSocket.OPEN) {
        if (KeepAliveInterval.current) {
          window.clearInterval(KeepAliveInterval.current);
        }
        startKeepAlive();
        setWorkStatus(WSWorkStatus.OK);
      }
    }
  }, [endKeepAlive, isIdle, isOnline, sendMessage, startKeepAlive, workStatus]);

  useEffect(() => {
    if (!isOnline && !LostConnectionTimeout.current) {
      endKeepAlive({ reason: "User gone offline" });
      log("Start waiting for internet connection restoring");
      setWorkStatus(WSWorkStatus.Offline);

      LostConnectionTimeout.current = window.setTimeout(() => {
        setWorkStatus(WSWorkStatus.ERROR);
        setCloseStatus(WSCloseStatus.OfflineTimeoutError);
        log("Internet connection was unsuccessful in 60 seconds. Reloading");
        closeWS({
          reason: WSCloseStatus.OfflineTimeoutError,
          skip_message: true,
        });
      }, 60_000);
    }
  }, [closeWS, endKeepAlive, isIdle, isOnline, isOpen, startKeepAlive]);

  useEffect(() => {
    if (
      WS.current &&
      isOpen &&
      isOnline &&
      !isIdle &&
      !KeepAliveWorking.current
    ) {
      log("User active");
      startKeepAlive();
      setWorkStatus(WSWorkStatus.OK);
      window.clearTimeout(LostConnectionTimeout.current);
      LostConnectionTimeout.current = null;
    }
  }, [isIdle, isOnline, isOpen, startKeepAlive]);

  useEffect(() => {
    if (isReconnecting && connectionAttempts > 0) {
      openWS();
    } else if (connectionAttempts <= 0) {
      setIsReconnecting(false);
      setCloseStatus(WSCloseStatus.ReconnectError);
      setWorkStatus(WSWorkStatus.ERROR);
      window.clearInterval(ReconnectInterval.current);
    }
  }, [connectionAttempts, isReconnecting, openWS]);

  useEffect(() => {
    if (isOpen && !isIdle && workStatus === WSWorkStatus.OK) {
      // Refresh token every 8 minutes for active WebSocket users
      const activityRefreshInterval = window.setInterval(async () => {
        const success = await refreshTokenIfActive();
        if (!success) {
          log("Activity-based refresh failed, trying regular refresh");
          try {
            const newToken = await refreshToken();
            if (!newToken) {
              log("All token refresh attempts failed, closing WebSocket");
              closeWS({ reason: "Token refresh failed" });
              openWSModal({
                workStatus: WSWorkStatus.ERROR,
                closeStatus: WSCloseStatus.AuthError
              });
              // Force logout after a short delay to allow modal to show
              setTimeout(() => {
                localStorage.clear();
                window.location.href = "/login";
              }, 2000);
            } else {
              log("Regular token refresh succeeded after activity-based refresh failed");
            }
          } catch (error) {
            console.error("Token refresh error:", error);
            closeWS({ reason: "Token refresh error" });
          }
        }
      }, 8 * 60 * 1000);
      
      return () => window.clearInterval(activityRefreshInterval);
    }
  }, [isOpen, isIdle, workStatus, refreshTokenIfActive, closeWS, openWSModal]);

  useEffect(() => {
    return () => {
      if (KeepAliveInterval.current)
        window.clearInterval(KeepAliveInterval.current);
      if (ReconnectInterval.current)
        window.clearInterval(ReconnectInterval.current);
      if (LostConnectionTimeout.current)
        window.clearTimeout(LostConnectionTimeout.current);
    };
  }, []);

  useEffect(() => {
    if (workStatus) log(`Work status: ${workStatus}`);
  }, [workStatus]);

  useEffect(() => {
    if (closeStatus) log(`Close status: ${closeStatus}`);
  }, [closeStatus]);

  return (
    <WSContext.Provider
      value={{
        isOpen,
        isReconnecting,
        closeStatus,
        workStatus,
        close: closeWS,
        open: openWS,
        openWSModal,
        sendMessage,
      }}
    >
      <WSStatusModal
        CloseStatus={closeStatus}
        WorkStatus={workStatus}
        close={closeWS}
      />
      {children}
    </WSContext.Provider>
  );
};
