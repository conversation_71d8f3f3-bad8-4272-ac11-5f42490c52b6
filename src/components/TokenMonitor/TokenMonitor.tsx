import { useEffect } from 'react';
import { refreshToken, isTokenRefreshInProgress } from '../../api/tokenRefresh';

export const TokenMonitor = () => {
  useEffect(() => {
    // First check shortly after component mounts
    const initialCheck = setTimeout(() => {
        try {
          // Only refresh if not already in progress
          if (!isTokenRefreshInProgress()) {
            refreshToken()
              .then(token => {
                if (token) {
                  console.debug("Initial token refresh successful");
                }
              })
              .catch(err => console.error("Initial token refresh failed:", err));
          }
        } catch (e) {
          console.error("Error during initial token refresh attempt:", e);
        }
      }, 3000); // Wait 3 seconds for app to initialize properly
          
    // Then check token freshness periodically
    const tokenCheckInterval = setInterval(() => {
      const userAuthString = localStorage.getItem("userAuth");
      if (!userAuthString) return;
      
      try {
        const userAuth = JSON.parse(userAuthString);
        if (!userAuth.token) return;
        
        const tokenDate = new Date(userAuth.date);
        const now = new Date();
        const tokenAgeMinutes = (now.getTime() - tokenDate.getTime()) / (1000 * 60);
        
        // Only refresh if token is older than 9 minutes and no refresh is in progress
        // This aligns with WebSocket's 8-minute refresh to avoid conflicts
        if (tokenAgeMinutes > 9 && !isTokenRefreshInProgress()) {
          refreshToken()
            .catch(err => console.error("TokenMonitor refresh failed:", err));
        }
      } catch (error) {
        console.error("Token monitoring error:", error);
      }
    }, 2 * 60 * 1000); // Check every 2 minutes
    
    return () => {
      clearTimeout(initialCheck);
      clearInterval(tokenCheckInterval);
    };
  }, []);
  
  return null;
};