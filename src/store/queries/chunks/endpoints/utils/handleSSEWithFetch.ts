export async function handleSSEWithFetch(url: string) {
  const UserAuth = localStorage.getItem("userAuth");

  const response = await fetch(url, {
    method: "GET",
    headers: {
      Authorization: `Bearer ${JSON.parse(UserAuth).token}`,
      Accept: "text/event-stream",
      "x-api-key": import.meta.env.VITE_API_KEY,
    },
    signal: AbortSignal.timeout(20_000),
  });

  if (!response.ok) {
    throw {
      message: `HTTP error! Status: ${response.status}`,
      status: response.status,
    };
  }

  const reader = response.body.getReader();
  const decoder = new TextDecoder("utf-8");
  let buffer = "";
  const result = [];

  // eslint-disable-next-line no-constant-condition
  while (true) {
    const { done, value } = await reader.read();
    if (done) break;

    buffer += decoder.decode(value, { stream: true });

    const lines = buffer.split("\n");

    for (const line of lines) {
      if (line.startsWith("data:")) {
        const data = line.substring(5).trim();
        result.push(data);
      }
    }
  }

  return result;
}
