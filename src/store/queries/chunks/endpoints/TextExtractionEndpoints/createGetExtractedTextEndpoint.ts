import { getApiUrl } from "../../../../../api/getApiUrl.ts";
import { ApiBuilder } from "../../../base.query.ts";
import { extractOcrText } from "../utils/extractOcrText.ts";
import { handleSSEWithFetch } from "../utils/handleSSEWithFetch.ts";

export const createGetExtractedTextEndpoint = (
  build: ApiBuilder<"ChunksApi">,
) => {
  return build.mutation<{ text_array: string[] }, { screenshot_id: string }>({
    queryFn: async ({ screenshot_id }) => {
      try {
        const text_array = await handleSSEWithFetch(
          getApiUrl({
            url: `/chunk/extract_text/${screenshot_id}`,
          }),
        );

        return {
          data: {
            text_array: text_array
              .map(extractOcrText)
              .filter((text) => text !== null),
          },
        };
      } catch (error) {
        return { error };
      }
    },
  });
};
