# stage 1 – build assets
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
# inject VITE_ vars at build
ARG VITE_API_URL
ARG VITE_WS_URL
ARG VITE_API_KEY
ARG VITE_DEVELOPMENT_MODE=false
ARG VITE_VERSION="1.0.0"
ENV VITE_API_URL=$VITE_API_URL \
    VITE_WS_URL=$VITE_WS_URL \
    VITE_API_KEY=$VITE_API_KEY \
    VITE_DEVELOPMENT_MODE=$VITE_DEVELOPMENT_MODE \
    VITE_VERSION=$VITE_VERSION
RUN npm run build

# stage 2 – use node serve
FROM node:18-alpine
WORKDIR /app
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/serve.json ./serve.json
RUN npm install --production serve
EXPOSE 3000
CMD ["npm", "run", "serve"]