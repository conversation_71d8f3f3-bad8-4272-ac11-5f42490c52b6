FROM python:3.12-alpine
WORKDIR /app
ENV PYTHONDONTWRITEBYTECODE=1 PYTHONUNBUFFERED=1
# install bash for scripts & openssl if needed
RUN apk update \
 && apk add --no-cache bash openssl \
 && apk upgrade --no-cache
COPY . /app
RUN pip install --no-cache-dir -r requirements.txt
EXPOSE ${API_PORT:-8000}
ENTRYPOINT ["/bin/sh","-lc","\
  if [ -n \"$API_CERT_PATH\" ] && [ -n \"$API_KEYFILE_PATH\" ]; then \
    alembic -c infrastructure/database/alembic.ini upgrade head && \
    exec uvicorn main:app --host 0.0.0.0 --port ${API_PORT:-8000} \
      --ssl-certfile \"$API_CERT_PATH\" --ssl-keyfile \"$API_KEYFILE_PATH\"; \
  else \
    alembic -c infrastructure/database/alembic.ini upgrade head && \
    exec uvicorn main:app --host 0.0.0.0 --port ${API_PORT:-8000}; \
  fi \
"]