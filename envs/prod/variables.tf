variable "tenant" {}
variable "environment" {}
variable "account_id" {}
variable "cidr_block" {}
variable "region" {}
variable "enable_cf" { default = false }
variable "sns_endpoint" {
  type    = list(any)
  default = []
}
variable "enable_nat_gateway" {

}

variable "tags" {

}
variable "security_groups" {

}
variable "ssm_backend" {}
variable "rds_name" {}
variable "rds_username" {}
variable "rds_engine" {}
variable "rds_engine_version" {}
variable "apply_immediately" {}
variable "rds_allocated_storage" {}
variable "rds_instance_class" {}
variable "rds_port" {}
variable "rds_multi_az" {}
variable "rds_family" {}
variable "sso_admin_role" {}
variable "domain" {}