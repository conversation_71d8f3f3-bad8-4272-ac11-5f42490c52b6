module "us-east-2" {
  source = "../../resources"

  cidr_block            = var.cidr_block
  environment           = var.environment
  tenant                = var.tenant
  account_id            = var.account_id
  region                = var.region
  sns_endpoint          = var.sns_endpoint
  enable_nat_gateway    = var.enable_nat_gateway
  security_groups       = var.security_groups
  tags                  = var.tags
  rds_name              = var.rds_name
  rds_engine            = var.rds_engine
  rds_engine_version    = var.rds_engine_version
  apply_immediately     = var.apply_immediately
  rds_allocated_storage = var.rds_allocated_storage
  rds_instance_class    = var.rds_instance_class
  rds_port              = var.rds_port
  rds_multi_az          = var.rds_multi_az
  rds_family            = var.rds_family
  rds_username          = var.rds_username
  sso_admin_role        = var.sso_admin_role
  ssm_backend           = var.ssm_backend
  dns_zone              = var.domain
  providers = {
    aws = aws.us-east-2
  }
}