from fastapi import Depends
from fastapi.security import <PERSON>A<PERSON>2<PERSON><PERSON><PERSON><PERSON>earer
from sqlalchemy.ext.asyncio import AsyncSession

from application.schemas.user_schemas import User<PERSON>ist<PERSON>neUser
from application.services import AuthService, AuthServiceImpl
from infrastructure.database.connection_config import get_session


oauth2_scheme: OAuth2PasswordBearer = OAuth2PasswordBearer(
    tokenUrl="/api/v1/auth/token", scheme_name="User JWT"
)


async def get_current_user(
    token: str = Depends(oauth2_scheme), session: AsyncSession = Depends(get_session)
) -> UserListOneUser:
    """
    Retrieve the current authenticated user based on the provided token.

    This function is used to extract the authenticated user from the request's token.
    The token is passed via the `oauth2_scheme` dependency and is then decoded and validated to fetch the user information.

    Args:
        token (str): The authentication token passed in the request, retrieved using the `oauth2_scheme` dependency.
        session (AsyncSession): The database session, retrieved through the `get_session` dependency.

    Returns:
        UserListOneUser: The user associated with the provided token.

    Raises:
        HTTPException: If the token is invalid or expired, an HTTP exception will be raised indicating authentication failure.
    """
    auth_service: AuthService = AuthServiceImpl(session)
    return await auth_service.get_user_from_token(token)
