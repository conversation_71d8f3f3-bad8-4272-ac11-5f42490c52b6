import io
from typing import Annotated
from datetime import datetime, UTC

from fastapi import APIRouter, Depends, Form, Query, Request, status, HTTPException
from fastapi.responses import ORJSONResponse, StreamingResponse

from application.schemas import (
    ResponseDetailsSchema, TotpUriSchema, UserLogoutSuccessSchema, UserSigninSchema,
    UserSigninTokenSchema, UserSignupFormSchema, UserSignupSchema, UserListOneUser
)
from application.services import AuthService
from infrastructure.logs import get_logger
from presentation.dependencies import get_current_user
from presentation.rest.dependencies import get_auth_service
from utils import get_masked_value

from ..forms import OAuth2PasswordAndRefreshRequestForm
from jose import jwt, JWTError
from infrastructure.settings import Config

logger = get_logger(__name__)
router = APIRouter(prefix="/auth", tags=["Auth"], default_response_class=ORJSONResponse)


@logger.catch
@router.post(
    "/token",
    summary="Create access and refresh tokens for user",
    response_model=UserSigninTokenSchema,
    responses={
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Incorrect email or password or refresh token"
        },
        status.HTTP_403_FORBIDDEN: {
            "description": "Could not validate refresh token or email is not verified"
        },
        status.HTTP_404_NOT_FOUND: {"description": "Could not find user"},
    },
)
async def create_token(
    request: Request,
    form_data: OAuth2PasswordAndRefreshRequestForm = Depends(),
    auth_service: AuthService = Depends(get_auth_service),
) -> ORJSONResponse:
    """
    Handles user login and generates access and refresh tokens.

    Args:
        request (Request): The incoming request object.
        form_data (OAuth2PasswordAndRefreshRequestForm): The form data containing the user's credentials.
        auth_service (AuthService): The authentication service used to process login.

    Returns:
        ORJSONResponse: A response containing the generated tokens.

    Responses:
        200 OK: Successfully authenticated.
        401 Unauthorized: Incorrect email or password, or refresh token.
        403 Forbidden: Could not validate the refresh token, or email is not verified.
        404 Not Found: Could not find the user.
    """
    logger.info(f"Login attempt for user: {form_data.username}")
    content: dict = await auth_service.login(request, form_data)
    logger.info(f"User {form_data.username} authenticated successfully")
    return ORJSONResponse(content=content)


@logger.catch
@router.post(
    "/signup",
    summary="Create new user",
    response_model=UserSignupSchema,
    responses={
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Incorrect email or password or refresh token"
        },
        status.HTTP_403_FORBIDDEN: {
            "description": "Could not validate refresh token or email is not verified"
        },
        status.HTTP_404_NOT_FOUND: {"description": "Could not find user"},
    },
)
async def create_user(
    request: Request,
    form_data: Annotated[UserSignupFormSchema, Form()],
    auth_service: AuthService = Depends(get_auth_service),
) -> ORJSONResponse:
    """
    Handles user signup and creates a new user.

    Args:
        request (Request): The incoming request object.
        form_data (UserSignupFormSchema): The form data containing the user's signup information.
        auth_service (AuthService): The authentication service used to process signup.

    Returns:
        ORJSONResponse: A response containing the created user's username.

    Responses:
        201 Created: User successfully created.
        401 Unauthorized: Incorrect email or password, or refresh token.
        403 Forbidden: Could not validate the refresh token, or email is not verified.
        404 Not Found: Could not find the user.
    """
    logger.info(f"Signup attempt for user: {form_data.username}")
    username: str = await auth_service.signup(form_data, request)
    logger.info(f"User {form_data.username} signed up successfully")
    return ORJSONResponse(
        status_code=status.HTTP_201_CREATED,
        content=UserSignupSchema(username=username).model_dump(),
    )


@logger.catch
@router.post(
    "/otp/verify",
    summary="Verify OTP",
    response_model=ResponseDetailsSchema,
    responses={
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Incorrect email or password or refresh token"
        },
        status.HTTP_403_FORBIDDEN: {
            "description": "Could not validate refresh token or email is not verified"
        },
        status.HTTP_404_NOT_FOUND: {"description": "Could not find user"},
    },
)
async def post_auth_otp_verify(
    access_token: str = Form(default=""),
    totp_code: str = Form(default=""),
    auth_service: AuthService = Depends(get_auth_service),
) -> ORJSONResponse:
    """
    Verifies the OTP for user authentication.

    Args:
        access_token (str): The access token associated with the user.
        totp_code (str): The OTP code to be verified.
        auth_service (AuthService): The authentication service used to verify the OTP.

    Returns:
        ORJSONResponse: A response indicating whether the OTP was successfully verified.

    Responses:
        200 OK: OTP successfully verified.
        401 Unauthorized: Incorrect email or password, or refresh token.
        403 Forbidden: Could not validate the refresh token, or email is not verified.
        404 Not Found: Could not find the user.
    """
    logger.info(
        f"OTP verification attempt with code: {await get_masked_value(totp_code)}"
    )
    await auth_service.otp_verify(access_token, totp_code)
    logger.info("OTP verified successfully")
    return ORJSONResponse(content={"details": "TOTP verified"})


@logger.catch
@router.post(
    "/otp/enable",
    summary="Enable 2FA with QR code or URL and add to Authenticator app",
    # response_model=dict[str, PacketsDelegatedListSchema | int],
    # response_class=ORJSONResponse | StreamingResponse,
    responses={
        status.HTTP_401_UNAUTHORIZED: {"description": "Invalid token"},
        status.HTTP_403_FORBIDDEN: {"description": "Not enough permissions"},
    },
)
async def get_auth_otp_uri_type(
    uri_type_schema: TotpUriSchema = Query(),
    access_token: str = Form(default=""),
    auth_service: AuthService = Depends(get_auth_service),
):  # -> ORJSONResponse | StreamingResponse:
    """
    Enables two-factor authentication (2FA) and provides a QR code or URL to add to the Authenticator app.

    Args:
        uri_type_schema (TotpUriSchema): The URI type for the OTP (QR code or URL).
        access_token (str): The access token associated with the user.
        auth_service (AuthService): The authentication service used to enable OTP.

    Returns:
        ORJSONResponse | StreamingResponse: A response containing either a QR code (as an image) or the OTP URI details.

    Responses:
        200 OK: OTP successfully enabled.
        401 Unauthorized: Invalid token.
        403 Forbidden: Not enough permissions.
    """
    logger.info("OTP enable attempt")
    connection_object: bytes | str = await auth_service.otp_enable(
        access_token, uri_type_schema.uri_type
    )
    logger.info("OTP enabled successfully")
    if isinstance(connection_object, bytes):
        return StreamingResponse(io.BytesIO(connection_object), media_type="image/png")
    else:
        return ORJSONResponse(content={"details": connection_object})


@logger.catch
@router.post(
    "/logout",
    summary="Logout user",
    response_model=UserLogoutSuccessSchema,
    responses={
        status.HTTP_401_UNAUTHORIZED: {"description": "Invalid token"},
    },
)
async def logout_user(
    request: Request,
    auth_service: AuthService = Depends(get_auth_service),
) -> ORJSONResponse:
    # Get token from Authorization header
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        return ORJSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={"detail": "Invalid or missing token"}
        )
    
    token = auth_header.replace("Bearer ", "")
    user = None
    
    try:
        # Try standard validation first
        payload = jwt.decode(
            token, 
            Config.JWT_SECRET_KEY, 
            algorithms=[Config.ALGORITHM]
        )
        username = payload.get("sub")
        if username:
            user = await auth_service.user_service.get_user(username)
    except JWTError:
        # If that fails, try without validating expiration
        try:
            payload = jwt.decode(
                token, 
                Config.JWT_SECRET_KEY,
                options={"verify_exp": False},  # Skip expiration check
                algorithms=[Config.ALGORITHM]
            )
            username = payload.get("sub")
            if username:
                user = await auth_service.user_service.get_user(username)
        except Exception as e:
            logger.error(f"Error decoding token: {e}")
    
    if user:
        logger.info(f"Logout attempt for user: {user.username}")
        content: dict = await auth_service.logout(user)
        logger.info(f"User {user.username} logged out successfully")
        return ORJSONResponse(content=content)
    
    return ORJSONResponse(
        status_code=status.HTTP_401_UNAUTHORIZED,
        content={"detail": "Could not identify user from token"}
    )


@router.post(
    "/refresh-if-active",
    summary="Refresh token if user has been active",
    response_model=UserSigninTokenSchema,
)
async def refresh_if_active(
    request: Request,
    user: UserListOneUser = Depends(get_current_user),
    auth_service: AuthService = Depends(get_auth_service),
) -> ORJSONResponse:
    """
    Refreshes tokens if user has been active in the last 10 minutes.
    This allows active users to stay logged in indefinitely.
    """
    try:
        # If user has never been tracked, update last_online now
        if not user.last_online:
            await auth_service.user_repository.update_fields(
                user.user_id, 
                auth_service.session, 
                **{"last_online": datetime.now(UTC)}
            )
            await auth_service.session.commit()
            user.last_online = datetime.now(UTC)
        
        time_since_activity = datetime.now(UTC) - user.last_online
        
        # Allow up to 10 minutes of inactivity for active WebSocket users
        if time_since_activity.total_seconds() < 600:  # 10 minutes
            # Generate new tokens
            data_to_encode = {"sub": user.username, "user_type": user.user_type}
            access_token, _ = await auth_service.jwt_service.create_access_token(data=data_to_encode)
            refresh_token, _ = await auth_service.jwt_service.create_refresh_token(data=data_to_encode)
            
            # Update last_online since they're actively refreshing
            await auth_service.user_repository.update_fields(
                user.user_id, 
                auth_service.session, 
                **{"last_online": datetime.now(UTC)}
            )
            await auth_service.session.commit()
            
            return ORJSONResponse(content={
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": "Bearer",
                "user_type": user.user_type,
                "user_id": str(user.user_id),
                "authenticator_connect_requested": user.authenticator_connect_requested,
            })
    except Exception as e:
        logger.error(f"Error in refresh-if-active: {e}")
        raise
    
    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="User has been inactive"
    )
