---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: qa-backend
  namespace: training
  annotations:
    reloader.stakater.com/auto: "true"
spec:
  replicas: 1
  revisionHistoryLimit: 0
  selector:
    matchLabels:
      app: qa-backend
  template:
    metadata:
      labels:
        app: qa-backend
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: assignment
                    operator: In
                    values:
                      - training
      tolerations:
        - key: "training"
          operator: Equal
          value: "true"
          effect: "NoExecute"
      serviceAccountName: qa-backend-training
      # volumes:
      #   - name: secrets-store-inline
      #     csi:
      #       driver: secrets-store.csi.k8s.io
      #       readOnly: true
      #       volumeAttributes:
      #         secretProviderClass: qa-backend
      dnsPolicy: ClusterFirst
      containers:
        - name: qa-backend
          image: ************.dkr.ecr.us-east-2.amazonaws.com/qa-backend:a9d6a204
          # readinessProbe:
          #   httpGet:
          #     path: /health
          #     port: 3000
          #   initialDelaySeconds: 30
          #   periodSeconds: 15
          #   timeoutSeconds: 5 
          # livenessProbe:
          #   httpGet:
          #     path: /health  
          #     port: 3000
          #   initialDelaySeconds: 30 
          #   periodSeconds: 15
          #   timeoutSeconds: 5
          resources:
            limits:
              memory: 1000Mi
              cpu: 500m
            requests:
              memory: 200Mi
              cpu: 200m
          # envFrom:
          #   - secretRef:
          #       name: qa-backend
          env:
            - name: API_HOST
              value: "0.0.0.0"
            - name: API_PORT
              value: "8000"
            - name: RABBITMQ_HOST
              value: rabbitmq.rabbitmq.svc.cluster.local
            - name: RABBITMQ_PORT
              value: "5672"
            - name: RABBITMQ_DEFAULT_USER
              value: user
            - name: RABBITMQ_DEFAULT_PASS
              value: 1hENwBdjtrGj29Tg
            - name: RABBITMQ_DEFAULT_VHOST
              value: "/"
            - name: RABBITMQ_QUEUE_SUB_PACKET_INCOMING
              value: "training_to_backend_qa"
            - name: RABBITMQ_QUEUE_SUB_PACKET_SKIPPED_INCOMING
              value: "training_to_backend_over_qa"
            - name: RABBITMQ_QUEUE_SUB_PACKET_STATUS
              value: "training_packet_status"
            - name: RABBITMQ_QUEUE_SUB_PACKET_SCREENSHOT
              value: "training_to_screenshot_postprocess"
            - name: RABBITMQ_QUEUE_PUB
              value: "training_from_backend_qa"
            - name: RABBITMQ_QUEUE_PUB_TO_QA_POSTPROCESSOR
              value: "training_queue_from_backend_qa"
            - name: FRONTEND_URL 
              value: "https://training-qa-frontend-stage.atomadvantage.ai"
            - name: SOURCE_SECRET_KEY
              value: "zqnR3gKxR2h_xZ9YqL8n7XgP6sW5tY3jK2mN1bC0vFg="
            - name: AWS_S3_BUCKET
              value: "atom-advantage-packets-stage"
            - name: ACCESS_TOKEN_EXPIRE_MINUTES
              value: "1440"
            - name: API_KEY
              value: "b4e9ae3a8e1f5c6f2a0d9c8b7e6a5d4c3b2a1f0e9d8c7b6a5f4e3d2c1b0a9f8e"
            - name: DB_HOST
              value: "backend-training.cpowm4cec1q3.us-east-2.rds.amazonaws.com"
            - name: DB_PORT
              value: "5432"
            - name: DB_NAME
              value: "backend_training_stage"
            - name: DB_USER
              value: "backend"
            - name: DB_PASSWORD
              value: "uT9ASw3d8YSRdxxN"
            - name: JWT_REFRESH_SECRET_KEY
              value: "kV1lVs1gW10Pcu4Pa6e0fxUpwCzFbNYjGtRHtPCbhMy"
            - name: JWT_SECRET_KEY
              value: "PmgfWi2c7X0VQ5XvMvFvqVZ8yFG3TkVdAuwslRPzTzK"
            - name: MAIL_TOKEN
              value: "4562be90340fae1bd6d7ad2e6b17f6f1c506eebd3d230076c5712af81cb594a2"
            - name: MAILGUN_API_DOMAIN
              value: "mg.atomadvantage.ai"
            - name: MAILGUN_API_KEY
              value: "**************************************************"
          # volumeMounts:
          #   - name: secrets-store-inline
          #     mountPath: /secrets
