from datetime import datetime, timed<PERSON><PERSON>, UTC
from typing import Sequence
from uuid import UUID
from zoneinfo import ZoneInfo

from aiohttp import BasicAuth, ClientSession
from pydantic import BaseModel, ConfigDict
from sqlalchemy import RowMapping

from application.schemas import (
    LargePacketMissingChunksListSchema, MailingDBSchema, PacketNotificationListSchema,
    StuckedDelegatedChunkSchema
)
from application.services import MetricService, PrometheusMetricService
from domain.repositories import (
    NotificationRepository, PacketChunkRepository, PacketRepository,
    UserPacketChunkRepository, UserRepository
)
from infrastructure.database.models import Packet, UserPacketChunk
from infrastructure.database.repositories.sqlalchemy import (
    SQLAlchemyChunkRepository, SQLAlchemyMailingRepository,
    SQLAlchemyNotificationRepository, SQLAlchemyPacketRepository,
    SQLAlchemyUserChunkRepository, SQLAlchemyUserRepository
)
from infrastructure.http_clients import InternalHttpClientImpl, PrometheusHTTPClientImpl
from infrastructure.logs import get_logger
from infrastructure.services.notifications import (
    EmailNotificationService, SlackNotificationService
)
from infrastructure.settings import Config


logger = get_logger(__name__)


class ExpiredPasswordSchema(BaseModel):
    user_id: UUID
    email: str
    username: str
    password_set: datetime
    pass_expire_mail: int

    model_config = ConfigDict(from_attributes=True)


@logger.catch
async def call_report_endpoint(period: str) -> None:
    """
    Calls the internal report endpoint to request a report for a specified period.

    This function is used to trigger the report generation process by making an HTTP request
    to the internal report endpoint. The `period` parameter determines which report to request
    (e.g., 'daily' or 'monthly').

    Args:
        period (str): The period for which the report is generated. Valid values are:
                       - 'daily' for a daily report
                       - 'monthly' for a monthly report

    Returns:
        None

    Notes:
        - The function is decorated with `@logger.catch` to automatically log any uncaught exceptions.
        - It delegates the actual HTTP request to the `call_report_endpoint` method of the `InternalHttpClientImpl`.
        - The HTTP request uses the provided period to determine the type of report to request.
    """
    await InternalHttpClientImpl.call_report_endpoint(period)


@logger.catch
async def send_packet_prometheus_metrics() -> None:
    """
    Sends Prometheus metrics related to packet processing in the last 24 hours.

    This function calculates various metrics for packet processing and sends them to
    Prometheus. It retrieves the average packet processing time and the packet throughput
    for the last 24 hours, and then sends those metrics to a Prometheus server. It also
    sends additional metrics related to the status of packets in the system.

    The function performs the following steps:
    - Retrieves the average packet processing time over the last 24 hours.
    - Retrieves the total number of packets processed in the last 24 hours.
    - Sends these metrics to Prometheus.
    - Sends additional metrics related to packets currently in the system or leaving the system.

    Returns:
        None

    Notes:
        - The function makes use of `PacketRepository` to query packet-related data.
        - Metrics are sent using `PrometheusHTTPClientImpl` for packet time and count.
        - The `MetricService` sends additional status-related metrics for packets.
        - The function assumes the time zone is "America/New_York" for the calculation of the 24-hour period.
    """
    packet_repository: PacketRepository = SQLAlchemyPacketRepository()
    user_tz: ZoneInfo = ZoneInfo("America/New_York")
    end_time: datetime = datetime.now(user_tz)
    start_time: datetime = end_time - timedelta(days=1)
    conditions: list = [Packet.sent_time >= start_time, Packet.sent_time <= end_time]
    # TODO: extract metrics in metric service
    average_package_time_24h: float = (
        await packet_repository.get_avg_packet_overall_for_24h(conditions)
    ).total_seconds()
    average_package_count_24h: float = float(
        await packet_repository.get_throughput_for_24h(conditions)
    )
    metric_service: MetricService = PrometheusMetricService("atom_pipeline")

    base_dict: dict = {
        "application": "atom_pipeline",
        "type": "gauge",
        "additional_parameters": {"type": "web"},
    }

    # await PrometheusHTTPClientImpl.send_metric(
    #     name="average_package_time_24h",
    #     value=average_package_time_24h,
    #     help_="The metric corresponds to the average number of seconds per packet for the last 24h",
    #     base_dict=base_dict,
    # )

    # await PrometheusHTTPClientImpl.send_metric(
    #     name="average_package_count_24h",
    #     value=average_package_count_24h,
    #     help_="The metric corresponds to the total number of packets for the last 24h",
    #     base_dict=base_dict,
    # )

    await metric_service.send_packet_status_metric(
        metric_name="packets_in_system",
        metric_help="The metric corresponds to the total number of packets in QA Tool or Pipeline",
        status="in_system",
    )

    await metric_service.send_packet_status_metric(
        metric_name="packets_out_system",
        metric_help="The metric corresponds to the total number of packets out off QA Tool and Pipeline",
        status="out_system",
    )


@logger.catch
async def check_user_password_expire_time() -> None:
    """
    Checks and sends notifications for users whose passwords have expired or are nearing expiration.

    This function performs the following tasks:
    - Retrieves a list of users with expired passwords from the repository.
    - Determines the status of each user's password expiration and whether a notification should be sent.
    - Sends an email notification to users whose passwords have been expired for 85, 89, or 90 days.
    - Updates the user's `pass_expire_mail` and `active` fields in the database.

    The function checks the user's password set time and sends the following notifications:
    - For users whose passwords have been set for 90+ days, the account is locked, and they receive an "Account Locked" email.
    - For passwords set for 89 days, the user receives an "Urgent" reminder email.
    - For passwords set for 85 days, the user receives a "Reminder" email.

    It also updates the database to reflect the password expiration status and sends the respective notifications.

    Returns:
        None

    Notes:
        - The time zone used for checking expiration is UTC.
        - The function depends on the `UserRepository` for retrieving user data and updating user fields.
        - The `EmailNotificationService` is used to send email notifications to the users.
        - The function will only send notifications if the password expiration is within 85-90 days, based on the time the password was last set.
        - The function utilizes Mailgun's API for sending email notifications.
    """
    user_repository: UserRepository = SQLAlchemyUserRepository()
    email_notification_service: EmailNotificationService = EmailNotificationService()
    users_with_expired_password: Sequence[
        RowMapping
    ] = await user_repository.get_expired_passwords()
    expired_passwords: list[dict] = [
        ExpiredPasswordSchema.model_validate(user_password).model_dump()
        for user_password in users_with_expired_password
    ]
    now_: datetime = datetime.now(UTC)
    for expired_password in expired_passwords:
        expired_password_set: datetime = expired_password["password_set"]
        pass_expire_mail: int = expired_password["pass_expire_mail"]
        user_id: UUID = expired_password["user_id"]
        username: str = expired_password["username"]
        email: str = expired_password["email"]

        to_update: dict = {"pass_expire_mail": pass_expire_mail}
        if expired_password_set < now_ - timedelta(days=90):
            if to_update["pass_expire_mail"] == 90:
                continue
            to_update["pass_expire_mail"] = 90
            to_update.update({"active": False})
            subject: str = "Account Locked: Update Your Password"
        elif expired_password_set < now_ - timedelta(days=89):
            if to_update["pass_expire_mail"] == 89:
                continue
            to_update["pass_expire_mail"] = 89
            subject: str = "Urgent: Update Your Password"
        else:
            if to_update["pass_expire_mail"] == 85:
                continue
            to_update["pass_expire_mail"] = 85
            subject: str = "Reminder: Update Your Password"

        await user_repository.update_fields(user_id, None, **to_update)
        data: dict = {
            "username": username,
            "days_since_last_update": to_update["pass_expire_mail"],
            "link": f"{Config.FRONTEND_URL}",
        }

        async with ClientSession(
            auth=BasicAuth("api", Config.MAILGUN_API_KEY)
        ) as aio_session:
            await email_notification_service.send(
                data, "expired_password_warning.html", aio_session, email, subject
            )


@logger.catch
async def check_chunk_update_time() -> None:
    """
    Checks for chunks stuck in the review process and updates their status.

    This cron job identifies packets that have been in review for too long,
    notifies the responsible team via email, and updates the packet's status
    if necessary.

    Returns:
        None

    Raises:
        ValueError: If the mailing list for stuck review packets is not found.
    """
    chunk_repository: PacketChunkRepository = SQLAlchemyChunkRepository()
    user_chunk_repository: UserPacketChunkRepository = SQLAlchemyUserChunkRepository()
    notification_repository: NotificationRepository = SQLAlchemyNotificationRepository()
    mailing_repository: SQLAlchemyMailingRepository = SQLAlchemyMailingRepository()
    email_notification_service: EmailNotificationService = EmailNotificationService()

    stuck_review_chunks: list[dict] = await chunk_repository.get_stuck_review_chunks()
    now_: datetime = datetime.now(UTC)
    status_mapping: dict[str, str] = {
        "supervisor_review": "delegated",
        "in_review": "pending",
    }
    if stuck_review_chunks:
        team_mailing: (
            MailingDBSchema | None
        ) = await mailing_repository.get_by_name_wo_session("stuck_review_chunk")

        if not team_mailing:
            logger.error("Mailing list 'stuck_review_chunk' not found.")
            return

        team_emails: list[str] = team_mailing.mailing_emails
        for stuck_review_chunk in stuck_review_chunks:
            email_data: dict = stuck_review_chunk | {
                "mail_datetime": now_,
                "login_link": f"{Config.FRONTEND_URL}/login",
                "environment": Config.RUN_ENV,
            }
            notification_data: dict = {
                "entity_id": stuck_review_chunk["chunk_id"],
                "entity": "chunk",
                "notification_type": "chunk_review_stuck",
                "email": True,
            }

            async with ClientSession(
                auth=BasicAuth("api", Config.MAILGUN_API_KEY)
            ) as aio_session:
                await email_notification_service.send(
                    email_data,
                    "stuck_review_chunk.html",
                    aio_session,
                    team_emails,
                    "🚨 Chunk Stuck in Review: Immediate Action Required",
                )
            chunk_new_status: str | None = status_mapping.get(
                stuck_review_chunk.get("status", {}), None
            )
            chunk_update: dict = {}
            if chunk_new_status:
                await user_chunk_repository.update_chunk_wo_session(
                    stuck_review_chunk.get("chunk_id", ""), True, **{"active": None}
                )
                chunk_update["status"] = chunk_new_status
            await chunk_repository.update_fields_wo_session(
                stuck_review_chunk.get("chunk_id", ""), **chunk_update
            )
            await notification_repository.create_notification_wo_session(
                notification_data, True
            )


@logger.catch
async def check_stucked_delegated_chunks() -> None:
    """
    Checks for stuck delegated chunks and sends email notifications.

    This cron job identifies chunks that have been delegated but not processed
    within a certain timeframe and notifies the relevant team via email.

    Returns:
        None

    Raises:
        ValueError: If no email recipients are found for notifications.
    """
    chunk_repository: PacketChunkRepository = SQLAlchemyChunkRepository()
    email_notification_service: EmailNotificationService = EmailNotificationService()
    mailing_repository: SQLAlchemyMailingRepository = SQLAlchemyMailingRepository()
    notification_repository: NotificationRepository = SQLAlchemyNotificationRepository()
    user_chunk_repository: UserPacketChunkRepository = SQLAlchemyUserChunkRepository()

    team_mailing: (
        MailingDBSchema | None
    ) = await mailing_repository.get_by_name_wo_session("stuck_delegated_chunk")

    if not team_mailing.mailing_emails:
        logger.warning(
            "Couldn't find emails to send notification about stucked delegated chunks."
        )
    else:
        while old_delegated_doc := await chunk_repository.get_old_delegated_chunk():
            validated_stucked_chunk: dict = StuckedDelegatedChunkSchema.model_validate(
                old_delegated_doc
            ).model_dump()

            user_chunk: (
                UserPacketChunk | None
            ) = await user_chunk_repository.get_user_packet_chunk_with_delegated_time(
                validated_stucked_chunk["chunk_id"]
            )
            email_data: dict = {
                "chunk_id": validated_stucked_chunk["chunk_id"],
                "filename": validated_stucked_chunk["filename"],
                "last_opened": f"{user_chunk.last_opened}",
                "delegated": f"{user_chunk.delegated}",
                "run_env": f"{Config.RUN_ENV}",
                "frontend_url": f"{Config.FRONTEND_URL}",
            }

            notification_data: dict = {
                "entity_id": old_delegated_doc.chunk_id,
                "entity": "chunk",
                "notification_type": "chunk_delegated_stuck",
                "email": True,
            }

            async with ClientSession(
                auth=BasicAuth("api", Config.MAILGUN_API_KEY)
            ) as aio_session:
                await email_notification_service.send(
                    email_data,
                    "delegated_chunk_stucked.html",
                    aio_session,
                    team_mailing.mailing_emails,
                    "🚨 Document Queue Alert: Review Needed",
                )

            logger.info(
                f"Sent email for stucked delegated chunk with ID: {old_delegated_doc.chunk_id}"
            )
            await notification_repository.create_notification_wo_session(
                notification_data, True
            )


@logger.catch
async def check_stucked_large_packets_chunks() -> None:
    """
    Checks for large packets with missing chunks and sends an email notification if any are found.

    This function performs the following tasks:
    - Retrieves a list of large packets with missing chunks from the repository.
    - If any such packets are found, it prepares and sends an email notification to the team.
    - The email contains a report of the stuck packets and is sent using Mailgun.

    The function checks the `PacketRepository` for large packets that have missing chunks, which can indicate an issue with packet processing. If any such packets are found, it fetches the team mailing list from the `MailingRepository` and sends a notification email with the list of packets.

    Returns:
        None

    Notes:
        - The function uses `SQLAlchemyPacketRepository` for fetching large packets and `SQLAlchemyMailingRepository` for retrieving the team mailing list.
        - The email is sent using Mailgun's API, with the `EmailNotificationService` responsible for composing and sending the message.
        - The function assumes that the team mailing list is stored under the name "team".
        - The report includes the environment and current date/time for context.
        - The function uses the `UTC` time zone for the report's timestamp.
    """
    packet_repository: PacketRepository = SQLAlchemyPacketRepository()
    email_notification_service: EmailNotificationService = EmailNotificationService()
    mailing_repository: SQLAlchemyMailingRepository = SQLAlchemyMailingRepository()

    large_packet_with_missing_chunks: Sequence = (
        LargePacketMissingChunksListSchema(
            packets=await packet_repository.get_large_packets_with_missing_chunks()
        )
        .model_dump()
        .get("packets", [])
    )

    if large_packet_with_missing_chunks:
        team_mailing: (
            MailingDBSchema | None
        ) = await mailing_repository.get_by_name_wo_session("team")

        email_data: dict = {
            "mail_rows": large_packet_with_missing_chunks,
            "body": {
                "environment": Config.RUN_ENV,
                "report_date": datetime.now(UTC).strftime("%m/%d/%Y %H:%M:%S"),
            },
        }

        async with ClientSession(
            auth=BasicAuth("api", Config.MAILGUN_API_KEY)
        ) as aio_session:
            await email_notification_service.send(
                email_data,
                "stucked_chunks_large_packet.html",
                aio_session,
                team_mailing.mailing_emails,
                "🚨 Stucked chunks of large packet",
            )
        logger.info("Sent email for stucked large packet chunks.")


@logger.catch
async def check_stucked_packets_before_qa() -> None:
    """
    Checks for packets that are stuck in the pipeline before reaching QA, and sends notifications to Slack.

    This function performs the following tasks:
    - Retrieves a list of packets that are stuck in the pipeline (in the *pipeline_in* status for over 20 minutes).
    - For each stuck packet, it generates a Slack notification message containing details about the packet.
    - The notification is sent to a specified Slack channel using the `SlackNotificationService`.
    - A record of the notification is created in the `NotificationRepository`.

    The function checks the `PacketRepository` for packets that have been stuck in the pipeline for an extended period (over 20 minutes), which could indicate an issue in the pipeline process. Once detected, it sends an alert to Slack with the packet details and logs the notification in the system.

    Returns:
        None

    Notes:
        - The function uses `SQLAlchemyPacketRepository` for fetching the stuck packets and `SQLAlchemyNotificationRepository` for saving the notification record.
        - Notifications are sent using `SlackNotificationService`, with details about the packet being stuck before QA.
        - The notification includes the packet's ID, pipeline packet ID, filename, creation time, whether it's a large packet, and the number of chunks.
        - The function uses the environment variable `Config.RUN_ENV` to identify the environment for the notification.
        - If any packets are found, it logs how many notifications were sent.
    """
    packet_repository: PacketRepository = SQLAlchemyPacketRepository()
    notification_repository: NotificationRepository = SQLAlchemyNotificationRepository()
    slack_notification_service = SlackNotificationService(
        Config.SLACK_NOTIFICATIONS_CHANNEL
    )

    packets: Sequence[dict] = (
        PacketNotificationListSchema(
            packets=await packet_repository.get_stucked_in_pipeline_before_qa_wo_session()
        )
        .model_dump(exclude_none=True)
        .get("packets", [{}])
    )

    for packet in packets:
        notification_text: str = (
            "🚨 *Packet Stuck Alert!* 🚨\n\n"
            "Packet stuck in *pipeline_in* status for 20 minutes! :fire_engine:\n\n"
            f"*• Environment:* `{Config.RUN_ENV}`\n"
            f"*• Packet ID:* `{packet['packet_id']}`\n"
            f"*• Pipeline packet ID:* `{packet['pipeline_packet_id']}`\n"
            f"*• Filename:* `{packet['filename']}`\n"
            f"*• Received by pipeline at:* `{packet['created_at']}`\n"
            f"*• Is large:* `{packet['is_large']}`\n"
            f"*• Chunks amount:* `{packet.get('chunks_amount', '-')}`"
        )
        notification_data: dict = {
            "entity_id": packet["packet_id"],
            "entity": "packet",
            "notification_type": "packet_stuck_before_qa",
            "slack": True,
        }
        await slack_notification_service.send(notification_text)
        await notification_repository.create_notification_wo_session(
            details=notification_data, commit=True
        )

    if packets:
        logger.info(
            f"Sent notifications about {len(packets)} "
            f"packets stucked before QA Tool"
        )


@logger.catch
async def check_stucked_packets_after_qa() -> None:
    """
    Checks for packets that are stuck in the pipeline after passing through QA, and sends notifications to Slack.

    This function performs the following tasks:
    - Retrieves a list of packets that are stuck in the pipeline (in the *qa_out* status for over 10 minutes).
    - For each stuck packet, it generates a Slack notification message containing details about the packet.
    - The notification is sent to a specified Slack channel using the `SlackNotificationService`.
    - A record of the notification is created in the `NotificationRepository`.

    The function checks the `PacketRepository` for packets that have been stuck in the pipeline after passing through QA (in the *qa_out* status for over 10 minutes). Once detected, it sends an alert to Slack with the packet details and logs the notification in the system.

    Returns:
        None

    Notes:
        - The function uses `SQLAlchemyPacketRepository` for fetching the stuck packets and `SQLAlchemyNotificationRepository` for saving the notification record.
        - Notifications are sent using `SlackNotificationService`, with details about the packet being stuck after QA.
        - The notification includes the packet's ID, pipeline packet ID, filename, sent time to pipeline, whether it's a large packet, and the number of chunks.
        - The function uses the environment variable `Config.RUN_ENV` to identify the environment for the notification.
        - If any packets are found, it logs how many notifications were sent.
    """
    packet_repository: PacketRepository = SQLAlchemyPacketRepository()
    notification_repository: NotificationRepository = SQLAlchemyNotificationRepository()
    slack_notification_service = SlackNotificationService(
        Config.SLACK_NOTIFICATIONS_CHANNEL
    )

    packets: Sequence[dict] = (
        PacketNotificationListSchema(
            packets=await packet_repository.get_stucked_after_qa_wo_session()
        )
        .model_dump(exclude_none=True)
        .get("packets", [{}])
    )

    for packet in packets:
        notification_text: str = (
            "🚨 *Packet Stuck Alert!* 🚨\n\n"
            "Packet stuck in *qa_out* status for 10 minutes! :fire_engine:\n\n"
            f"*• Environment:* `{Config.RUN_ENV}`\n"
            f"*• Packet ID:* `{packet['packet_id']}`\n"
            f"*• Pipeline packet ID:* `{packet['pipeline_packet_id']}`\n"
            f"*• Filename:* `{packet['filename']}`\n"
            f"*• Sent to pipeline at:* `{packet['sent_time']}`\n"
            f"*• Is large:* `{packet['is_large']}`\n"
            f"*• Chunks amount:* `{packet.get('chunks_amount', '-')}`"
        )
        notification_data: dict = {
            "entity_id": packet["packet_id"],
            "entity": "packet",
            "notification_type": "packet_stuck_after_qa",
            "slack": True,
        }
        await slack_notification_service.send(notification_text)
        await notification_repository.create_notification_wo_session(
            details=notification_data, commit=True
        )

    if packets:
        logger.info(
            f"Sent notifications about {len(packets)} " f"packets stucked after QA Tool"
        )
