import time
from contextlib import asynccontextmanager
import traceback

import uvicorn
from aiohttp import BasicAuth, ClientSession
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from application.services import MailingService, MailingServiceImpl
from infrastructure.database.connection_config import async_session
from infrastructure.logs import interceptor  # noqa: F401
from infrastructure.messaging.rmq import rabbit_router
from infrastructure.scheduling import jobs
from infrastructure.settings import Config
from presentation.rest.routes import docs_router, rest_routes
from presentation.ws.routes import router as ws_router


async def check_startup_required_data() -> None:
    async with async_session() as session:
        mailing_service: MailingService = MailingServiceImpl(session)
        if missing_mailings := await mailing_service.check_required_mailings_missing():
            raise RuntimeError(
                f"Need to add next mailings for application startup: {', '.join(missing_mailings)}"
            )


def init_app():
    @asynccontextmanager
    async def lifespan(app_: FastAPI):
        await check_startup_required_data()

        scheduler: AsyncIOScheduler = AsyncIOScheduler()
        for job in jobs:
            scheduler.add_job(**job)
        scheduler.start()

        async with ClientSession(
            auth=BasicAuth("api", Config.MAILGUN_API_KEY)
        ) as aio_session:
            yield {"aio_session": aio_session, "pub_sub_broker": rabbit_router.broker}
            scheduler.shutdown()

    app_ = FastAPI(
        title="Atom API",
        description="API",
        version="1.0.5",
        lifespan=lifespan,
        docs_url=None,
        redoc_url=None,
        openapi_url=None,
    )
    app_.add_middleware(
        CORSMiddleware,
        allow_origins=["https://qa-frontend-stage.atomadvantage.ai", "https://portal.atomadvantage.ai", "*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    @app_.middleware("http")
    async def log_exceptions_middleware(request: Request, call_next):
        try:
            return await call_next(request)
        except Exception as e:
            tb_str = traceback.format_exc()
            print(f"Unhandled exception: {e}\nTraceback:\n{tb_str}")
            raise e

    @app_.middleware("http")
    async def add_process_time_header(request: Request, call_next):
        start_time = time.time()
        response = await call_next(request)
        process_time = time.time() - start_time
        response.headers["X-Process-Time"] = str(process_time)
        return response

    @app_.middleware("websocket")
    async def skip_api_key_for_ws(ws, call_next):
        return await call_next(ws)

    @app_.middleware("http")
    async def check_api_key_header(request: Request, call_next):
        api_key: str | None = request.headers.get("X-Api-Key")
        if (not api_key or api_key != Config.API_KEY) and request.method != "OPTIONS":
            return JSONResponse({"details": "Invalid API Key"}, status_code=403)
        response = await call_next(request)
        return response

    routes: list = rest_routes + [ws_router]
    for r in routes:
        app_.include_router(r, prefix="/api/v1")

    app_.include_router(docs_router)
    app_.include_router(rabbit_router)

    return app_


app = init_app()

if __name__ == "__main__":
    run_kwargs = {
        "host": Config.API_HOST,
        "port": Config.API_PORT,
        "log_config": None,
        "log_level": None,
    }
    if Config.API_CERT_PATH and Config.API_KEYFILE_PATH:
        run_kwargs["ssl_certfile"] = Config.API_CERT_PATH
        run_kwargs["ssl_keyfile"]  = Config.API_KEYFILE_PATH

    uvicorn.run("main:app", **run_kwargs)
